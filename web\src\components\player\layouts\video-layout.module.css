.controls {
  /*
   * These CSS variables are supported out of the box to easily apply offsets to all popups.
   * You can also offset via props on `Tooltip.Content`, `Menu.Content`, and slider previews.
   */
  --media-tooltip-y-offset: 30px;
  --media-menu-y-offset: 30px;
  --video-controls-focus: #0284c7;
  --video-controls-hover: white;
  --video-controls: #e5e7eb;
}

.controls[data-visible] {
  opacity: 1;
}

.controls:hover {
  opacity: 1;
}

.controls :global(.volume-slider) {
  --media-slider-preview-offset: 30px;
  margin-left: 1.5px;
}
