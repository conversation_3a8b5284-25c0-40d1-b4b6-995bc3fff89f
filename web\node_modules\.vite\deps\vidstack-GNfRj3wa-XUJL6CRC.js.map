{"version": 3, "sources": ["../../@vidstack/react/dev/chunks/vidstack-GNfRj3wa.js"], "sourcesContent": ["\"use client\"\n\nimport { peek, listenEvent, effect, DOMEvent, isString, camelToKebabCase, isUndefined, isFunction } from './vidstack-CH225ns1.js';\nimport { QualitySymbol, RAFLoop, TextTrack, TextTrackSymbol, ListSymbol, IS_CHROME, coerceToError, loadScript, VideoProvider, isHLSSupported, preconnect } from './vidstack-C-WrcxmD.js';\nimport 'react';\nimport '@floating-ui/dom';\n\nconst toDOMEventType = (type) => camelToKebabCase(type);\nclass HLSController {\n  #video;\n  #ctx;\n  #instance = null;\n  #stopLiveSync = null;\n  config = {};\n  #callbacks = /* @__PURE__ */ new Set();\n  get instance() {\n    return this.#instance;\n  }\n  constructor(video, ctx) {\n    this.#video = video;\n    this.#ctx = ctx;\n  }\n  setup(ctor) {\n    const { streamType } = this.#ctx.$state;\n    const isLive = peek(streamType).includes(\"live\"), isLiveLowLatency = peek(streamType).includes(\"ll-\");\n    this.#instance = new ctor({\n      lowLatencyMode: isLiveLowLatency,\n      backBufferLength: isLiveLowLatency ? 4 : isLive ? 8 : void 0,\n      renderTextTracksNatively: false,\n      ...this.config\n    });\n    const dispatcher = this.#dispatchHLSEvent.bind(this);\n    for (const event of Object.values(ctor.Events)) this.#instance.on(event, dispatcher);\n    this.#instance.on(ctor.Events.ERROR, this.#onError.bind(this));\n    for (const callback of this.#callbacks) callback(this.#instance);\n    this.#ctx.player.dispatch(\"hls-instance\", {\n      detail: this.#instance\n    });\n    this.#instance.attachMedia(this.#video);\n    this.#instance.on(ctor.Events.AUDIO_TRACK_SWITCHED, this.#onAudioSwitch.bind(this));\n    this.#instance.on(ctor.Events.LEVEL_SWITCHED, this.#onLevelSwitched.bind(this));\n    this.#instance.on(ctor.Events.LEVEL_LOADED, this.#onLevelLoaded.bind(this));\n    this.#instance.on(ctor.Events.LEVEL_UPDATED, this.#onLevelUpdated.bind(this));\n    this.#instance.on(ctor.Events.NON_NATIVE_TEXT_TRACKS_FOUND, this.#onTracksFound.bind(this));\n    this.#instance.on(ctor.Events.CUES_PARSED, this.#onCuesParsed.bind(this));\n    this.#ctx.qualities[QualitySymbol.enableAuto] = this.#enableAutoQuality.bind(this);\n    listenEvent(this.#ctx.qualities, \"change\", this.#onUserQualityChange.bind(this));\n    listenEvent(this.#ctx.audioTracks, \"change\", this.#onUserAudioChange.bind(this));\n    this.#stopLiveSync = effect(this.#liveSync.bind(this));\n  }\n  #createDOMEvent(type, data) {\n    return new DOMEvent(toDOMEventType(type), { detail: data });\n  }\n  #liveSync() {\n    if (!this.#ctx.$state.live()) return;\n    const raf = new RAFLoop(this.#liveSyncPosition.bind(this));\n    raf.start();\n    return raf.stop.bind(raf);\n  }\n  #liveSyncPosition() {\n    this.#ctx.$state.liveSyncPosition.set(this.#instance?.liveSyncPosition ?? Infinity);\n  }\n  #dispatchHLSEvent(type, data) {\n    this.#ctx.player?.dispatch(this.#createDOMEvent(type, data));\n  }\n  #onTracksFound(eventType, data) {\n    const event = this.#createDOMEvent(eventType, data);\n    let currentTrack = -1;\n    for (let i = 0; i < data.tracks.length; i++) {\n      const nonNativeTrack = data.tracks[i], init = nonNativeTrack.subtitleTrack ?? nonNativeTrack.closedCaptions, track = new TextTrack({\n        id: `hls-${nonNativeTrack.kind}-${i}`,\n        src: init?.url,\n        label: nonNativeTrack.label,\n        language: init?.lang,\n        kind: nonNativeTrack.kind,\n        default: nonNativeTrack.default\n      });\n      track[TextTrackSymbol.readyState] = 2;\n      track[TextTrackSymbol.onModeChange] = () => {\n        if (track.mode === \"showing\") {\n          this.#instance.subtitleTrack = i;\n          currentTrack = i;\n        } else if (currentTrack === i) {\n          this.#instance.subtitleTrack = -1;\n          currentTrack = -1;\n        }\n      };\n      this.#ctx.textTracks.add(track, event);\n    }\n  }\n  #onCuesParsed(eventType, data) {\n    const index = this.#instance?.subtitleTrack, track = this.#ctx.textTracks.getById(`hls-${data.type}-${index}`);\n    if (!track) return;\n    const event = this.#createDOMEvent(eventType, data);\n    for (const cue of data.cues) {\n      cue.positionAlign = \"auto\";\n      track.addCue(cue, event);\n    }\n  }\n  #onAudioSwitch(eventType, data) {\n    const track = this.#ctx.audioTracks[data.id];\n    if (track) {\n      const trigger = this.#createDOMEvent(eventType, data);\n      this.#ctx.audioTracks[ListSymbol.select](track, true, trigger);\n    }\n  }\n  #onLevelSwitched(eventType, data) {\n    const quality = this.#ctx.qualities[data.level];\n    if (quality) {\n      const trigger = this.#createDOMEvent(eventType, data);\n      this.#ctx.qualities[ListSymbol.select](quality, true, trigger);\n    }\n  }\n  #onLevelUpdated(eventType, data) {\n    if (data.details.totalduration > 0) {\n      this.#ctx.$state.inferredLiveDVRWindow.set(data.details.totalduration);\n    }\n  }\n  #onLevelLoaded(eventType, data) {\n    if (this.#ctx.$state.canPlay()) return;\n    const { type, live, totalduration: duration, targetduration } = data.details, trigger = this.#createDOMEvent(eventType, data);\n    this.#ctx.notify(\n      \"stream-type-change\",\n      live ? type === \"EVENT\" && Number.isFinite(duration) && targetduration >= 10 ? \"live:dvr\" : \"live\" : \"on-demand\",\n      trigger\n    );\n    this.#ctx.notify(\"duration-change\", duration, trigger);\n    const media = this.#instance.media;\n    if (this.#instance.currentLevel === -1) {\n      this.#ctx.qualities[QualitySymbol.setAuto](true, trigger);\n    }\n    for (const remoteTrack of this.#instance.audioTracks) {\n      const localTrack = {\n        id: remoteTrack.id.toString(),\n        label: remoteTrack.name,\n        language: remoteTrack.lang || \"\",\n        kind: \"main\"\n      };\n      this.#ctx.audioTracks[ListSymbol.add](localTrack, trigger);\n    }\n    for (const level of this.#instance.levels) {\n      const videoQuality = {\n        id: level.id?.toString() ?? level.height + \"p\",\n        width: level.width,\n        height: level.height,\n        codec: level.codecSet,\n        bitrate: level.bitrate\n      };\n      this.#ctx.qualities[ListSymbol.add](videoQuality, trigger);\n    }\n    media.dispatchEvent(new DOMEvent(\"canplay\", { trigger }));\n  }\n  #onError(eventType, data) {\n    {\n      this.#ctx.logger?.errorGroup(`[vidstack] HLS error \\`${eventType}\\``).labelledLog(\"Media Element\", this.#instance?.media).labelledLog(\"HLS Instance\", this.#instance).labelledLog(\"Event Type\", eventType).labelledLog(\"Data\", data).labelledLog(\"Src\", peek(this.#ctx.$state.source)).labelledLog(\"Media Store\", { ...this.#ctx.$state }).dispatch();\n    }\n    if (data.fatal) {\n      switch (data.type) {\n        case \"mediaError\":\n          this.#instance?.recoverMediaError();\n          break;\n        default:\n          this.#onFatalError(data.error);\n          break;\n      }\n    }\n  }\n  #onFatalError(error) {\n    this.#ctx.notify(\"error\", {\n      message: error.message,\n      code: 1,\n      error\n    });\n  }\n  #enableAutoQuality() {\n    if (this.#instance) this.#instance.currentLevel = -1;\n  }\n  #onUserQualityChange() {\n    const { qualities } = this.#ctx;\n    if (!this.#instance || qualities.auto) return;\n    this.#instance[qualities.switch + \"Level\"] = qualities.selectedIndex;\n    if (IS_CHROME) {\n      this.#video.currentTime = this.#video.currentTime;\n    }\n  }\n  #onUserAudioChange() {\n    const { audioTracks } = this.#ctx;\n    if (this.#instance && this.#instance.audioTrack !== audioTracks.selectedIndex) {\n      this.#instance.audioTrack = audioTracks.selectedIndex;\n    }\n  }\n  onInstance(callback) {\n    this.#callbacks.add(callback);\n    return () => this.#callbacks.delete(callback);\n  }\n  loadSource(src) {\n    if (!isString(src.src)) return;\n    this.#instance?.loadSource(src.src);\n  }\n  destroy() {\n    this.#instance?.destroy();\n    this.#instance = null;\n    this.#stopLiveSync?.();\n    this.#stopLiveSync = null;\n    this.#ctx?.logger?.info(\"\\u{1F3D7}\\uFE0F Destroyed HLS instance\");\n  }\n}\n\nclass HLSLibLoader {\n  #lib;\n  #ctx;\n  #callback;\n  constructor(lib, ctx, callback) {\n    this.#lib = lib;\n    this.#ctx = ctx;\n    this.#callback = callback;\n    this.#startLoading();\n  }\n  async #startLoading() {\n    this.#ctx.logger?.info(\"\\u{1F3D7}\\uFE0F Loading HLS Library\");\n    const callbacks = {\n      onLoadStart: this.#onLoadStart.bind(this),\n      onLoaded: this.#onLoaded.bind(this),\n      onLoadError: this.#onLoadError.bind(this)\n    };\n    let ctor = await loadHLSScript(this.#lib, callbacks);\n    if (isUndefined(ctor) && !isString(this.#lib)) ctor = await importHLS(this.#lib, callbacks);\n    if (!ctor) return null;\n    if (!ctor.isSupported()) {\n      const message = \"[vidstack] `hls.js` is not supported in this environment\";\n      this.#ctx.logger?.error(message);\n      this.#ctx.player.dispatch(new DOMEvent(\"hls-unsupported\"));\n      this.#ctx.notify(\"error\", { message, code: 4 });\n      return null;\n    }\n    return ctor;\n  }\n  #onLoadStart() {\n    {\n      this.#ctx.logger?.infoGroup(\"Starting to load `hls.js`\").labelledLog(\"URL\", this.#lib).dispatch();\n    }\n    this.#ctx.player.dispatch(new DOMEvent(\"hls-lib-load-start\"));\n  }\n  #onLoaded(ctor) {\n    {\n      this.#ctx.logger?.infoGroup(\"Loaded `hls.js`\").labelledLog(\"Library\", this.#lib).labelledLog(\"Constructor\", ctor).dispatch();\n    }\n    this.#ctx.player.dispatch(\n      new DOMEvent(\"hls-lib-loaded\", {\n        detail: ctor\n      })\n    );\n    this.#callback(ctor);\n  }\n  #onLoadError(e) {\n    const error = coerceToError(e);\n    {\n      this.#ctx.logger?.errorGroup(\"[vidstack] Failed to load `hls.js`\").labelledLog(\"Library\", this.#lib).labelledLog(\"Error\", e).dispatch();\n    }\n    this.#ctx.player.dispatch(\n      new DOMEvent(\"hls-lib-load-error\", {\n        detail: error\n      })\n    );\n    this.#ctx.notify(\"error\", {\n      message: error.message,\n      code: 4,\n      error\n    });\n  }\n}\nasync function importHLS(loader, callbacks = {}) {\n  if (isUndefined(loader)) return void 0;\n  callbacks.onLoadStart?.();\n  if (loader.prototype && loader.prototype !== Function) {\n    callbacks.onLoaded?.(loader);\n    return loader;\n  }\n  try {\n    const ctor = (await loader())?.default;\n    if (ctor && !!ctor.isSupported) {\n      callbacks.onLoaded?.(ctor);\n    } else {\n      throw Error(\n        true ? \"[vidstack] failed importing `hls.js`. Dynamic import returned invalid constructor.\" : \"\"\n      );\n    }\n    return ctor;\n  } catch (err) {\n    callbacks.onLoadError?.(err);\n  }\n  return void 0;\n}\nasync function loadHLSScript(src, callbacks = {}) {\n  if (!isString(src)) return void 0;\n  callbacks.onLoadStart?.();\n  try {\n    await loadScript(src);\n    if (!isFunction(window.Hls)) {\n      throw Error(\n        true ? \"[vidstack] failed loading `hls.js`. Could not find a valid `Hls` constructor on window\" : \"\"\n      );\n    }\n    const ctor = window.Hls;\n    callbacks.onLoaded?.(ctor);\n    return ctor;\n  } catch (err) {\n    callbacks.onLoadError?.(err);\n  }\n  return void 0;\n}\n\nconst JS_DELIVR_CDN = \"https://cdn.jsdelivr.net\";\nclass HLSProvider extends VideoProvider {\n  $$PROVIDER_TYPE = \"HLS\";\n  #ctor = null;\n  #controller = new HLSController(this.video, this.ctx);\n  /**\n   * The `hls.js` constructor.\n   */\n  get ctor() {\n    return this.#ctor;\n  }\n  /**\n   * The current `hls.js` instance.\n   */\n  get instance() {\n    return this.#controller.instance;\n  }\n  /**\n   * Whether `hls.js` is supported in this environment.\n   */\n  static supported = isHLSSupported();\n  get type() {\n    return \"hls\";\n  }\n  get canLiveSync() {\n    return true;\n  }\n  #library = `${JS_DELIVR_CDN}/npm/hls.js@^1.5.0/dist/hls${\".js\" }`;\n  /**\n   * The `hls.js` configuration object.\n   *\n   * @see {@link https://github.com/video-dev/hls.js/blob/master/docs/API.md#fine-tuning}\n   */\n  get config() {\n    return this.#controller.config;\n  }\n  set config(config) {\n    this.#controller.config = config;\n  }\n  /**\n   * The `hls.js` constructor (supports dynamic imports) or a URL of where it can be found.\n   *\n   * @defaultValue `https://cdn.jsdelivr.net/npm/hls.js@^1.0.0/dist/hls.min.js`\n   */\n  get library() {\n    return this.#library;\n  }\n  set library(library) {\n    this.#library = library;\n  }\n  preconnect() {\n    if (!isString(this.#library)) return;\n    preconnect(this.#library);\n  }\n  setup() {\n    super.setup();\n    new HLSLibLoader(this.#library, this.ctx, (ctor) => {\n      this.#ctor = ctor;\n      this.#controller.setup(ctor);\n      this.ctx.notify(\"provider-setup\", this);\n      const src = peek(this.ctx.$state.source);\n      if (src) this.loadSource(src);\n    });\n  }\n  async loadSource(src, preload) {\n    if (!isString(src.src)) {\n      this.removeSource();\n      return;\n    }\n    this.media.preload = preload || \"\";\n    this.appendSource(src, \"application/x-mpegurl\");\n    this.#controller.loadSource(src);\n    this.currentSrc = src;\n  }\n  /**\n   * The given callback is invoked when a new `hls.js` instance is created and right before it's\n   * attached to media.\n   */\n  onInstance(callback) {\n    const instance = this.#controller.instance;\n    if (instance) callback(instance);\n    return this.#controller.onInstance(callback);\n  }\n  destroy() {\n    this.#controller.destroy();\n  }\n}\n\nexport { HLSProvider };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIA,mBAAO;AAGP,IAAM,iBAAiB,CAAC,SAAS,iBAAiB,IAAI;AAPtD;AAQA,IAAM,gBAAN,MAAoB;AAAA,EAUlB,YAAY,OAAO,KAAK;AAV1B;AACE;AACA;AACA,kCAAY;AACZ,sCAAgB;AAChB,kCAAS,CAAC;AACV,mCAA6B,oBAAI,IAAI;AAKnC,uBAAK,QAAS;AACd,uBAAK,MAAO;AAAA,EACd;AAAA,EANA,IAAI,WAAW;AACb,WAAO,mBAAK;AAAA,EACd;AAAA,EAKA,MAAM,MAAM;AACV,UAAM,EAAE,WAAW,IAAI,mBAAK,MAAK;AACjC,UAAM,SAAS,KAAK,UAAU,EAAE,SAAS,MAAM,GAAG,mBAAmB,KAAK,UAAU,EAAE,SAAS,KAAK;AACpG,uBAAK,WAAY,IAAI,KAAK;AAAA,MACxB,gBAAgB;AAAA,MAChB,kBAAkB,mBAAmB,IAAI,SAAS,IAAI;AAAA,MACtD,0BAA0B;AAAA,MAC1B,GAAG,KAAK;AAAA,IACV,CAAC;AACD,UAAM,aAAa,sBAAK,+CAAkB,KAAK,IAAI;AACnD,eAAW,SAAS,OAAO,OAAO,KAAK,MAAM,EAAG,oBAAK,WAAU,GAAG,OAAO,UAAU;AACnF,uBAAK,WAAU,GAAG,KAAK,OAAO,OAAO,sBAAK,sCAAS,KAAK,IAAI,CAAC;AAC7D,eAAW,YAAY,mBAAK,YAAY,UAAS,mBAAK,UAAS;AAC/D,uBAAK,MAAK,OAAO,SAAS,gBAAgB;AAAA,MACxC,QAAQ,mBAAK;AAAA,IACf,CAAC;AACD,uBAAK,WAAU,YAAY,mBAAK,OAAM;AACtC,uBAAK,WAAU,GAAG,KAAK,OAAO,sBAAsB,sBAAK,4CAAe,KAAK,IAAI,CAAC;AAClF,uBAAK,WAAU,GAAG,KAAK,OAAO,gBAAgB,sBAAK,8CAAiB,KAAK,IAAI,CAAC;AAC9E,uBAAK,WAAU,GAAG,KAAK,OAAO,cAAc,sBAAK,4CAAe,KAAK,IAAI,CAAC;AAC1E,uBAAK,WAAU,GAAG,KAAK,OAAO,eAAe,sBAAK,6CAAgB,KAAK,IAAI,CAAC;AAC5E,uBAAK,WAAU,GAAG,KAAK,OAAO,8BAA8B,sBAAK,4CAAe,KAAK,IAAI,CAAC;AAC1F,uBAAK,WAAU,GAAG,KAAK,OAAO,aAAa,sBAAK,2CAAc,KAAK,IAAI,CAAC;AACxE,uBAAK,MAAK,UAAU,cAAc,UAAU,IAAI,sBAAK,gDAAmB,KAAK,IAAI;AACjF,gBAAY,mBAAK,MAAK,WAAW,UAAU,sBAAK,kDAAqB,KAAK,IAAI,CAAC;AAC/E,gBAAY,mBAAK,MAAK,aAAa,UAAU,sBAAK,gDAAmB,KAAK,IAAI,CAAC;AAC/E,uBAAK,eAAgB,OAAO,sBAAK,uCAAU,KAAK,IAAI,CAAC;AAAA,EACvD;AAAA,EA8IA,WAAW,UAAU;AACnB,uBAAK,YAAW,IAAI,QAAQ;AAC5B,WAAO,MAAM,mBAAK,YAAW,OAAO,QAAQ;AAAA,EAC9C;AAAA,EACA,WAAW,KAAK;AAnMlB;AAoMI,QAAI,CAAC,SAAS,IAAI,GAAG,EAAG;AACxB,6BAAK,eAAL,mBAAgB,WAAW,IAAI;AAAA,EACjC;AAAA,EACA,UAAU;AAvMZ;AAwMI,6BAAK,eAAL,mBAAgB;AAChB,uBAAK,WAAY;AACjB,6BAAK,mBAAL;AACA,uBAAK,eAAgB;AACrB,mCAAK,UAAL,mBAAW,WAAX,mBAAmB,KAAK;AAAA,EAC1B;AACF;AArME;AACA;AACA;AACA;AAEA;AANF;AA0CE,oBAAe,SAAC,MAAM,MAAM;AAC1B,SAAO,IAAI,SAAS,eAAe,IAAI,GAAG,EAAE,QAAQ,KAAK,CAAC;AAC5D;AACA,cAAS,WAAG;AACV,MAAI,CAAC,mBAAK,MAAK,OAAO,KAAK,EAAG;AAC9B,QAAM,MAAM,IAAI,QAAQ,sBAAK,+CAAkB,KAAK,IAAI,CAAC;AACzD,MAAI,MAAM;AACV,SAAO,IAAI,KAAK,KAAK,GAAG;AAC1B;AACA,sBAAiB,WAAG;AA3DtB;AA4DI,qBAAK,MAAK,OAAO,iBAAiB,MAAI,wBAAK,eAAL,mBAAgB,qBAAoB,QAAQ;AACpF;AACA,sBAAiB,SAAC,MAAM,MAAM;AA9DhC;AA+DI,2BAAK,MAAK,WAAV,mBAAkB,SAAS,sBAAK,6CAAL,WAAqB,MAAM;AACxD;AACA,mBAAc,SAAC,WAAW,MAAM;AAC9B,QAAM,QAAQ,sBAAK,6CAAL,WAAqB,WAAW;AAC9C,MAAI,eAAe;AACnB,WAAS,IAAI,GAAG,IAAI,KAAK,OAAO,QAAQ,KAAK;AAC3C,UAAM,iBAAiB,KAAK,OAAO,CAAC,GAAG,OAAO,eAAe,iBAAiB,eAAe,gBAAgB,QAAQ,IAAI,UAAU;AAAA,MACjI,IAAI,OAAO,eAAe,IAAI,IAAI,CAAC;AAAA,MACnC,KAAK,6BAAM;AAAA,MACX,OAAO,eAAe;AAAA,MACtB,UAAU,6BAAM;AAAA,MAChB,MAAM,eAAe;AAAA,MACrB,SAAS,eAAe;AAAA,IAC1B,CAAC;AACD,UAAM,gBAAgB,UAAU,IAAI;AACpC,UAAM,gBAAgB,YAAY,IAAI,MAAM;AAC1C,UAAI,MAAM,SAAS,WAAW;AAC5B,2BAAK,WAAU,gBAAgB;AAC/B,uBAAe;AAAA,MACjB,WAAW,iBAAiB,GAAG;AAC7B,2BAAK,WAAU,gBAAgB;AAC/B,uBAAe;AAAA,MACjB;AAAA,IACF;AACA,uBAAK,MAAK,WAAW,IAAI,OAAO,KAAK;AAAA,EACvC;AACF;AACA,kBAAa,SAAC,WAAW,MAAM;AA1FjC;AA2FI,QAAM,SAAQ,wBAAK,eAAL,mBAAgB,eAAe,QAAQ,mBAAK,MAAK,WAAW,QAAQ,OAAO,KAAK,IAAI,IAAI,KAAK,EAAE;AAC7G,MAAI,CAAC,MAAO;AACZ,QAAM,QAAQ,sBAAK,6CAAL,WAAqB,WAAW;AAC9C,aAAW,OAAO,KAAK,MAAM;AAC3B,QAAI,gBAAgB;AACpB,UAAM,OAAO,KAAK,KAAK;AAAA,EACzB;AACF;AACA,mBAAc,SAAC,WAAW,MAAM;AAC9B,QAAM,QAAQ,mBAAK,MAAK,YAAY,KAAK,EAAE;AAC3C,MAAI,OAAO;AACT,UAAM,UAAU,sBAAK,6CAAL,WAAqB,WAAW;AAChD,uBAAK,MAAK,YAAY,WAAW,MAAM,EAAE,OAAO,MAAM,OAAO;AAAA,EAC/D;AACF;AACA,qBAAgB,SAAC,WAAW,MAAM;AAChC,QAAM,UAAU,mBAAK,MAAK,UAAU,KAAK,KAAK;AAC9C,MAAI,SAAS;AACX,UAAM,UAAU,sBAAK,6CAAL,WAAqB,WAAW;AAChD,uBAAK,MAAK,UAAU,WAAW,MAAM,EAAE,SAAS,MAAM,OAAO;AAAA,EAC/D;AACF;AACA,oBAAe,SAAC,WAAW,MAAM;AAC/B,MAAI,KAAK,QAAQ,gBAAgB,GAAG;AAClC,uBAAK,MAAK,OAAO,sBAAsB,IAAI,KAAK,QAAQ,aAAa;AAAA,EACvE;AACF;AACA,mBAAc,SAAC,WAAW,MAAM;AAtHlC;AAuHI,MAAI,mBAAK,MAAK,OAAO,QAAQ,EAAG;AAChC,QAAM,EAAE,MAAM,MAAM,eAAe,UAAU,eAAe,IAAI,KAAK,SAAS,UAAU,sBAAK,6CAAL,WAAqB,WAAW;AACxH,qBAAK,MAAK;AAAA,IACR;AAAA,IACA,OAAO,SAAS,WAAW,OAAO,SAAS,QAAQ,KAAK,kBAAkB,KAAK,aAAa,SAAS;AAAA,IACrG;AAAA,EACF;AACA,qBAAK,MAAK,OAAO,mBAAmB,UAAU,OAAO;AACrD,QAAM,QAAQ,mBAAK,WAAU;AAC7B,MAAI,mBAAK,WAAU,iBAAiB,IAAI;AACtC,uBAAK,MAAK,UAAU,cAAc,OAAO,EAAE,MAAM,OAAO;AAAA,EAC1D;AACA,aAAW,eAAe,mBAAK,WAAU,aAAa;AACpD,UAAM,aAAa;AAAA,MACjB,IAAI,YAAY,GAAG,SAAS;AAAA,MAC5B,OAAO,YAAY;AAAA,MACnB,UAAU,YAAY,QAAQ;AAAA,MAC9B,MAAM;AAAA,IACR;AACA,uBAAK,MAAK,YAAY,WAAW,GAAG,EAAE,YAAY,OAAO;AAAA,EAC3D;AACA,aAAW,SAAS,mBAAK,WAAU,QAAQ;AACzC,UAAM,eAAe;AAAA,MACnB,MAAI,WAAM,OAAN,mBAAU,eAAc,MAAM,SAAS;AAAA,MAC3C,OAAO,MAAM;AAAA,MACb,QAAQ,MAAM;AAAA,MACd,OAAO,MAAM;AAAA,MACb,SAAS,MAAM;AAAA,IACjB;AACA,uBAAK,MAAK,UAAU,WAAW,GAAG,EAAE,cAAc,OAAO;AAAA,EAC3D;AACA,QAAM,cAAc,IAAI,SAAS,WAAW,EAAE,QAAQ,CAAC,CAAC;AAC1D;AACA,aAAQ,SAAC,WAAW,MAAM;AAxJ5B;AAyJI;AACE,6BAAK,MAAK,WAAV,mBAAkB,WAAW,0BAA0B,SAAS,MAAM,YAAY,kBAAiB,wBAAK,eAAL,mBAAgB,OAAO,YAAY,gBAAgB,mBAAK,YAAW,YAAY,cAAc,WAAW,YAAY,QAAQ,MAAM,YAAY,OAAO,KAAK,mBAAK,MAAK,OAAO,MAAM,GAAG,YAAY,eAAe,EAAE,GAAG,mBAAK,MAAK,OAAO,GAAG;AAAA,EAC7U;AACA,MAAI,KAAK,OAAO;AACd,YAAQ,KAAK,MAAM;AAAA,MACjB,KAAK;AACH,iCAAK,eAAL,mBAAgB;AAChB;AAAA,MACF;AACE,8BAAK,2CAAL,WAAmB,KAAK;AACxB;AAAA,IACJ;AAAA,EACF;AACF;AACA,kBAAa,SAAC,OAAO;AACnB,qBAAK,MAAK,OAAO,SAAS;AAAA,IACxB,SAAS,MAAM;AAAA,IACf,MAAM;AAAA,IACN;AAAA,EACF,CAAC;AACH;AACA,uBAAkB,WAAG;AACnB,MAAI,mBAAK,WAAW,oBAAK,WAAU,eAAe;AACpD;AACA,yBAAoB,WAAG;AACrB,QAAM,EAAE,UAAU,IAAI,mBAAK;AAC3B,MAAI,CAAC,mBAAK,cAAa,UAAU,KAAM;AACvC,qBAAK,WAAU,UAAU,SAAS,OAAO,IAAI,UAAU;AACvD,MAAI,WAAW;AACb,uBAAK,QAAO,cAAc,mBAAK,QAAO;AAAA,EACxC;AACF;AACA,uBAAkB,WAAG;AACnB,QAAM,EAAE,YAAY,IAAI,mBAAK;AAC7B,MAAI,mBAAK,cAAa,mBAAK,WAAU,eAAe,YAAY,eAAe;AAC7E,uBAAK,WAAU,aAAa,YAAY;AAAA,EAC1C;AACF;AA9LF,UAAAA,OAAA;AAgNA,IAAM,eAAN,MAAmB;AAAA,EAIjB,YAAY,KAAK,KAAK,UAAU;AAJlC;AACE;AACA,uBAAAA;AACA;AAEE,uBAAK,MAAO;AACZ,uBAAKA,OAAO;AACZ,uBAAK,WAAY;AACjB,0BAAK,0CAAL;AAAA,EACF;AAqDF;AA7DE;AACAA,QAAA;AACA;AAHF;AAUQ,kBAAa,iBAAG;AA1NxB;AA2NI,2BAAKA,OAAK,WAAV,mBAAkB,KAAK;AACvB,QAAM,YAAY;AAAA,IAChB,aAAa,sBAAK,yCAAa,KAAK,IAAI;AAAA,IACxC,UAAU,sBAAK,sCAAU,KAAK,IAAI;AAAA,IAClC,aAAa,sBAAK,yCAAa,KAAK,IAAI;AAAA,EAC1C;AACA,MAAI,OAAO,MAAM,cAAc,mBAAK,OAAM,SAAS;AACnD,MAAI,YAAY,IAAI,KAAK,CAAC,SAAS,mBAAK,KAAI,EAAG,QAAO,MAAM,UAAU,mBAAK,OAAM,SAAS;AAC1F,MAAI,CAAC,KAAM,QAAO;AAClB,MAAI,CAAC,KAAK,YAAY,GAAG;AACvB,UAAM,UAAU;AAChB,6BAAKA,OAAK,WAAV,mBAAkB,MAAM;AACxB,uBAAKA,OAAK,OAAO,SAAS,IAAI,SAAS,iBAAiB,CAAC;AACzD,uBAAKA,OAAK,OAAO,SAAS,EAAE,SAAS,MAAM,EAAE,CAAC;AAC9C,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,iBAAY,WAAG;AA7OjB;AA8OI;AACE,6BAAKA,OAAK,WAAV,mBAAkB,UAAU,6BAA6B,YAAY,OAAO,mBAAK,OAAM;AAAA,EACzF;AACA,qBAAKA,OAAK,OAAO,SAAS,IAAI,SAAS,oBAAoB,CAAC;AAC9D;AACA,cAAS,SAAC,MAAM;AAnPlB;AAoPI;AACE,6BAAKA,OAAK,WAAV,mBAAkB,UAAU,mBAAmB,YAAY,WAAW,mBAAK,OAAM,YAAY,eAAe,MAAM;AAAA,EACpH;AACA,qBAAKA,OAAK,OAAO;AAAA,IACf,IAAI,SAAS,kBAAkB;AAAA,MAC7B,QAAQ;AAAA,IACV,CAAC;AAAA,EACH;AACA,qBAAK,WAAL,WAAe;AACjB;AACA,iBAAY,SAAC,GAAG;AA9PlB;AA+PI,QAAM,QAAQ,cAAc,CAAC;AAC7B;AACE,6BAAKA,OAAK,WAAV,mBAAkB,WAAW,sCAAsC,YAAY,WAAW,mBAAK,OAAM,YAAY,SAAS,GAAG;AAAA,EAC/H;AACA,qBAAKA,OAAK,OAAO;AAAA,IACf,IAAI,SAAS,sBAAsB;AAAA,MACjC,QAAQ;AAAA,IACV,CAAC;AAAA,EACH;AACA,qBAAKA,OAAK,OAAO,SAAS;AAAA,IACxB,SAAS,MAAM;AAAA,IACf,MAAM;AAAA,IACN;AAAA,EACF,CAAC;AACH;AAEF,eAAe,UAAU,QAAQ,YAAY,CAAC,GAAG;AA/QjD;AAgRE,MAAI,YAAY,MAAM,EAAG,QAAO;AAChC,kBAAU,gBAAV;AACA,MAAI,OAAO,aAAa,OAAO,cAAc,UAAU;AACrD,oBAAU,aAAV,mCAAqB;AACrB,WAAO;AAAA,EACT;AACA,MAAI;AACF,UAAM,QAAQ,WAAM,OAAO,MAAb,mBAAiB;AAC/B,QAAI,QAAQ,CAAC,CAAC,KAAK,aAAa;AAC9B,sBAAU,aAAV,mCAAqB;AAAA,IACvB,OAAO;AACL,YAAM;AAAA,QACJ,OAAO,uFAAuF;AAAA,MAChG;AAAA,IACF;AACA,WAAO;AAAA,EACT,SAAS,KAAK;AACZ,oBAAU,gBAAV,mCAAwB;AAAA,EAC1B;AACA,SAAO;AACT;AACA,eAAe,cAAc,KAAK,YAAY,CAAC,GAAG;AArSlD;AAsSE,MAAI,CAAC,SAAS,GAAG,EAAG,QAAO;AAC3B,kBAAU,gBAAV;AACA,MAAI;AACF,UAAM,WAAW,GAAG;AACpB,QAAI,CAAC,WAAW,OAAO,GAAG,GAAG;AAC3B,YAAM;AAAA,QACJ,OAAO,2FAA2F;AAAA,MACpG;AAAA,IACF;AACA,UAAM,OAAO,OAAO;AACpB,oBAAU,aAAV,mCAAqB;AACrB,WAAO;AAAA,EACT,SAAS,KAAK;AACZ,oBAAU,gBAAV,mCAAwB;AAAA,EAC1B;AACA,SAAO;AACT;AAEA,IAAM,gBAAgB;AAxTtB;AAyTA,IAAM,cAAN,cAA0B,cAAc;AAAA,EAAxC;AAAA;AACE,2CAAkB;AAClB,8BAAQ;AACR,oCAAc,IAAI,cAAc,KAAK,OAAO,KAAK,GAAG;AAuBpD,iCAAW,GAAG,aAAa,8BAA8B,KAAM;AAAA;AAAA;AAAA;AAAA;AAAA,EAnB/D,IAAI,OAAO;AACT,WAAO,mBAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,WAAW;AACb,WAAO,mBAAK,aAAY;AAAA,EAC1B;AAAA,EAKA,IAAI,OAAO;AACT,WAAO;AAAA,EACT;AAAA,EACA,IAAI,cAAc;AAChB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAI,SAAS;AACX,WAAO,mBAAK,aAAY;AAAA,EAC1B;AAAA,EACA,IAAI,OAAO,QAAQ;AACjB,uBAAK,aAAY,SAAS;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,UAAU;AACZ,WAAO,mBAAK;AAAA,EACd;AAAA,EACA,IAAI,QAAQ,SAAS;AACnB,uBAAK,UAAW;AAAA,EAClB;AAAA,EACA,aAAa;AACX,QAAI,CAAC,SAAS,mBAAK,SAAQ,EAAG;AAC9B,eAAW,mBAAK,SAAQ;AAAA,EAC1B;AAAA,EACA,QAAQ;AACN,UAAM,MAAM;AACZ,QAAI,aAAa,mBAAK,WAAU,KAAK,KAAK,CAAC,SAAS;AAClD,yBAAK,OAAQ;AACb,yBAAK,aAAY,MAAM,IAAI;AAC3B,WAAK,IAAI,OAAO,kBAAkB,IAAI;AACtC,YAAM,MAAM,KAAK,KAAK,IAAI,OAAO,MAAM;AACvC,UAAI,IAAK,MAAK,WAAW,GAAG;AAAA,IAC9B,CAAC;AAAA,EACH;AAAA,EACA,MAAM,WAAW,KAAK,SAAS;AAC7B,QAAI,CAAC,SAAS,IAAI,GAAG,GAAG;AACtB,WAAK,aAAa;AAClB;AAAA,IACF;AACA,SAAK,MAAM,UAAU,WAAW;AAChC,SAAK,aAAa,KAAK,uBAAuB;AAC9C,uBAAK,aAAY,WAAW,GAAG;AAC/B,SAAK,aAAa;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW,UAAU;AACnB,UAAM,WAAW,mBAAK,aAAY;AAClC,QAAI,SAAU,UAAS,QAAQ;AAC/B,WAAO,mBAAK,aAAY,WAAW,QAAQ;AAAA,EAC7C;AAAA,EACA,UAAU;AACR,uBAAK,aAAY,QAAQ;AAAA,EAC3B;AACF;AAnFE;AACA;AAuBA;AAAA;AAAA;AAAA;AAPA,cAnBI,aAmBG,aAAY,eAAe;", "names": ["_ctx"]}