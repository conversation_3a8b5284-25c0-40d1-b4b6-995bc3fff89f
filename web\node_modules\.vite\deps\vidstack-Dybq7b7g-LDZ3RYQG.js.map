{"version": 3, "sources": ["../../@vidstack/react/dev/chunks/vidstack-Dybq7b7g.js"], "sourcesContent": ["\"use client\"\n\nimport { listenEvent, effect, untrack, createScope, keysOf, onDispose, DOMEvent, peek } from './vidstack-CH225ns1.js';\nimport { ListSymbol, TimeRange, RAFLoop } from './vidstack-C-WrcxmD.js';\nimport { getCastSessionMedia, getCastContext, getCastSession, hasActiveCastSession, listenCastContextEvent, getCastErrorMessage } from './vidstack-C8ZxSSGF.js';\nimport 'react';\nimport '@floating-ui/dom';\n\nclass GoogleCastMediaInfoBuilder {\n  #info;\n  constructor(src) {\n    this.#info = new chrome.cast.media.MediaInfo(src.src, src.type);\n  }\n  build() {\n    return this.#info;\n  }\n  setStreamType(streamType) {\n    if (streamType.includes(\"live\")) {\n      this.#info.streamType = chrome.cast.media.StreamType.LIVE;\n    } else {\n      this.#info.streamType = chrome.cast.media.StreamType.BUFFERED;\n    }\n    return this;\n  }\n  setTracks(tracks) {\n    this.#info.tracks = tracks.map(this.#buildCastTrack);\n    return this;\n  }\n  setMetadata(title, poster) {\n    this.#info.metadata = new chrome.cast.media.GenericMediaMetadata();\n    this.#info.metadata.title = title;\n    this.#info.metadata.images = [{ url: poster }];\n    return this;\n  }\n  #buildCastTrack(track, trackId) {\n    const castTrack = new chrome.cast.media.Track(trackId, chrome.cast.media.TrackType.TEXT);\n    castTrack.name = track.label;\n    castTrack.trackContentId = track.src;\n    castTrack.trackContentType = \"text/vtt\";\n    castTrack.language = track.language;\n    castTrack.subtype = track.kind.toUpperCase();\n    return castTrack;\n  }\n}\n\nclass GoogleCastTracksManager {\n  #cast;\n  #ctx;\n  #onNewLocalTracks;\n  constructor(cast, ctx, onNewLocalTracks) {\n    this.#cast = cast;\n    this.#ctx = ctx;\n    this.#onNewLocalTracks = onNewLocalTracks;\n  }\n  setup() {\n    const syncRemoteActiveIds = this.syncRemoteActiveIds.bind(this);\n    listenEvent(this.#ctx.audioTracks, \"change\", syncRemoteActiveIds);\n    listenEvent(this.#ctx.textTracks, \"mode-change\", syncRemoteActiveIds);\n    effect(this.#syncLocalTracks.bind(this));\n  }\n  getLocalTextTracks() {\n    return this.#ctx.$state.textTracks().filter((track) => track.src && track.type === \"vtt\");\n  }\n  #getLocalAudioTracks() {\n    return this.#ctx.$state.audioTracks();\n  }\n  #getRemoteTracks(type) {\n    const tracks = this.#cast.mediaInfo?.tracks ?? [];\n    return type ? tracks.filter((track) => track.type === type) : tracks;\n  }\n  #getRemoteActiveIds() {\n    const activeIds = [], activeLocalAudioTrack = this.#getLocalAudioTracks().find((track) => track.selected), activeLocalTextTracks = this.getLocalTextTracks().filter((track) => track.mode === \"showing\");\n    if (activeLocalAudioTrack) {\n      const remoteAudioTracks = this.#getRemoteTracks(chrome.cast.media.TrackType.AUDIO), remoteAudioTrack = this.#findRemoteTrack(remoteAudioTracks, activeLocalAudioTrack);\n      if (remoteAudioTrack) activeIds.push(remoteAudioTrack.trackId);\n    }\n    if (activeLocalTextTracks?.length) {\n      const remoteTextTracks = this.#getRemoteTracks(chrome.cast.media.TrackType.TEXT);\n      if (remoteTextTracks.length) {\n        for (const localTrack of activeLocalTextTracks) {\n          const remoteTextTrack = this.#findRemoteTrack(remoteTextTracks, localTrack);\n          if (remoteTextTrack) activeIds.push(remoteTextTrack.trackId);\n        }\n      }\n    }\n    return activeIds;\n  }\n  #syncLocalTracks() {\n    const localTextTracks = this.getLocalTextTracks();\n    if (!this.#cast.isMediaLoaded) return;\n    const remoteTextTracks = this.#getRemoteTracks(chrome.cast.media.TrackType.TEXT);\n    for (const localTrack of localTextTracks) {\n      const hasRemoteTrack = this.#findRemoteTrack(remoteTextTracks, localTrack);\n      if (!hasRemoteTrack) {\n        untrack(() => this.#onNewLocalTracks?.());\n        break;\n      }\n    }\n  }\n  syncRemoteTracks(event) {\n    if (!this.#cast.isMediaLoaded) return;\n    const localAudioTracks = this.#getLocalAudioTracks(), localTextTracks = this.getLocalTextTracks(), remoteAudioTracks = this.#getRemoteTracks(chrome.cast.media.TrackType.AUDIO), remoteTextTracks = this.#getRemoteTracks(chrome.cast.media.TrackType.TEXT);\n    for (const remoteAudioTrack of remoteAudioTracks) {\n      const hasLocalTrack = this.#findLocalTrack(localAudioTracks, remoteAudioTrack);\n      if (hasLocalTrack) continue;\n      const localAudioTrack = {\n        id: remoteAudioTrack.trackId.toString(),\n        label: remoteAudioTrack.name,\n        language: remoteAudioTrack.language,\n        kind: remoteAudioTrack.subtype ?? \"main\",\n        selected: false\n      };\n      this.#ctx.audioTracks[ListSymbol.add](localAudioTrack, event);\n    }\n    for (const remoteTextTrack of remoteTextTracks) {\n      const hasLocalTrack = this.#findLocalTrack(localTextTracks, remoteTextTrack);\n      if (hasLocalTrack) continue;\n      const localTextTrack = {\n        id: remoteTextTrack.trackId.toString(),\n        src: remoteTextTrack.trackContentId,\n        label: remoteTextTrack.name,\n        language: remoteTextTrack.language,\n        kind: remoteTextTrack.subtype.toLowerCase()\n      };\n      this.#ctx.textTracks.add(localTextTrack, event);\n    }\n  }\n  syncRemoteActiveIds(event) {\n    if (!this.#cast.isMediaLoaded) return;\n    const activeIds = this.#getRemoteActiveIds(), editRequest = new chrome.cast.media.EditTracksInfoRequest(activeIds);\n    this.#editTracksInfo(editRequest).catch((error) => {\n      {\n        this.#ctx.logger?.errorGroup(\"[vidstack] failed to edit cast tracks info\").labelledLog(\"Edit Request\", editRequest).labelledLog(\"Error\", error).dispatch();\n      }\n    });\n  }\n  #editTracksInfo(request) {\n    const media = getCastSessionMedia();\n    return new Promise((resolve, reject) => media?.editTracksInfo(request, resolve, reject));\n  }\n  #findLocalTrack(localTracks, remoteTrack) {\n    return localTracks.find((localTrack) => this.#isMatch(localTrack, remoteTrack));\n  }\n  #findRemoteTrack(remoteTracks, localTrack) {\n    return remoteTracks.find((remoteTrack) => this.#isMatch(localTrack, remoteTrack));\n  }\n  // Note: we can't rely on id matching because they will differ between local/remote. A local\n  // track id might not even exist.\n  #isMatch(localTrack, remoteTrack) {\n    return remoteTrack.name === localTrack.label && remoteTrack.language === localTrack.language && remoteTrack.subtype.toLowerCase() === localTrack.kind.toLowerCase();\n  }\n}\n\nclass GoogleCastProvider {\n  $$PROVIDER_TYPE = \"GOOGLE_CAST\";\n  scope = createScope();\n  #player;\n  #ctx;\n  #tracks;\n  #currentSrc = null;\n  #state = \"disconnected\";\n  #currentTime = 0;\n  #played = 0;\n  #seekableRange = new TimeRange(0, 0);\n  #timeRAF = new RAFLoop(this.#onAnimationFrame.bind(this));\n  #playerEventHandlers;\n  #reloadInfo = null;\n  #isIdle = false;\n  constructor(player, ctx) {\n    this.#player = player;\n    this.#ctx = ctx;\n    this.#tracks = new GoogleCastTracksManager(player, ctx, this.#onNewLocalTracks.bind(this));\n  }\n  get type() {\n    return \"google-cast\";\n  }\n  get currentSrc() {\n    return this.#currentSrc;\n  }\n  /**\n   * The Google Cast remote player.\n   *\n   * @see {@link https://developers.google.com/cast/docs/reference/web_sender/cast.framework.RemotePlayer}\n   */\n  get player() {\n    return this.#player;\n  }\n  /**\n   * @see {@link https://developers.google.com/cast/docs/reference/web_sender/cast.framework.CastContext}\n   */\n  get cast() {\n    return getCastContext();\n  }\n  /**\n   * @see {@link https://developers.google.com/cast/docs/reference/web_sender/cast.framework.CastSession}\n   */\n  get session() {\n    return getCastSession();\n  }\n  /**\n   * @see {@link https://developers.google.com/cast/docs/reference/web_sender/chrome.cast.media.Media}\n   */\n  get media() {\n    return getCastSessionMedia();\n  }\n  /**\n   * Whether the current Google Cast session belongs to this provider.\n   */\n  get hasActiveSession() {\n    return hasActiveCastSession(this.#currentSrc);\n  }\n  setup() {\n    this.#attachCastContextEventListeners();\n    this.#attachCastPlayerEventListeners();\n    this.#tracks.setup();\n    this.#ctx.notify(\"provider-setup\", this);\n  }\n  #attachCastContextEventListeners() {\n    listenCastContextEvent(\n      cast.framework.CastContextEventType.CAST_STATE_CHANGED,\n      this.#onCastStateChange.bind(this)\n    );\n  }\n  #attachCastPlayerEventListeners() {\n    const Event2 = cast.framework.RemotePlayerEventType, handlers = {\n      [Event2.IS_CONNECTED_CHANGED]: this.#onCastStateChange,\n      [Event2.IS_MEDIA_LOADED_CHANGED]: this.#onMediaLoadedChange,\n      [Event2.CAN_CONTROL_VOLUME_CHANGED]: this.#onCanControlVolumeChange,\n      [Event2.CAN_SEEK_CHANGED]: this.#onCanSeekChange,\n      [Event2.DURATION_CHANGED]: this.#onDurationChange,\n      [Event2.IS_MUTED_CHANGED]: this.#onVolumeChange,\n      [Event2.VOLUME_LEVEL_CHANGED]: this.#onVolumeChange,\n      [Event2.IS_PAUSED_CHANGED]: this.#onPausedChange,\n      [Event2.LIVE_SEEKABLE_RANGE_CHANGED]: this.#onProgress,\n      [Event2.PLAYER_STATE_CHANGED]: this.#onPlayerStateChange\n    };\n    this.#playerEventHandlers = handlers;\n    const handler = this.#onRemotePlayerEvent.bind(this);\n    for (const type of keysOf(handlers)) {\n      this.#player.controller.addEventListener(type, handler);\n    }\n    onDispose(() => {\n      for (const type of keysOf(handlers)) {\n        this.#player.controller.removeEventListener(type, handler);\n      }\n    });\n  }\n  async play() {\n    if (!this.#player.isPaused && !this.#isIdle) return;\n    if (this.#isIdle) {\n      await this.#reload(false, 0);\n      return;\n    }\n    this.#player.controller?.playOrPause();\n  }\n  async pause() {\n    if (this.#player.isPaused) return;\n    this.#player.controller?.playOrPause();\n  }\n  getMediaStatus(request) {\n    return new Promise((resolve, reject) => {\n      this.media?.getStatus(request, resolve, reject);\n    });\n  }\n  setMuted(muted) {\n    const hasChanged = muted && !this.#player.isMuted || !muted && this.#player.isMuted;\n    if (hasChanged) this.#player.controller?.muteOrUnmute();\n  }\n  setCurrentTime(time) {\n    this.#player.currentTime = time;\n    this.#ctx.notify(\"seeking\", time);\n    this.#player.controller?.seek();\n  }\n  setVolume(volume) {\n    this.#player.volumeLevel = volume;\n    this.#player.controller?.setVolumeLevel();\n  }\n  async loadSource(src) {\n    if (this.#reloadInfo?.src !== src) this.#reloadInfo = null;\n    if (hasActiveCastSession(src)) {\n      this.#resumeSession();\n      this.#currentSrc = src;\n      return;\n    }\n    this.#ctx.notify(\"load-start\");\n    const loadRequest = this.#buildLoadRequest(src), errorCode = await this.session.loadMedia(loadRequest);\n    if (errorCode) {\n      this.#currentSrc = null;\n      this.#ctx.notify(\"error\", Error(getCastErrorMessage(errorCode)));\n      return;\n    }\n    this.#currentSrc = src;\n  }\n  destroy() {\n    this.#reset();\n    this.#endSession();\n  }\n  #reset() {\n    if (!this.#reloadInfo) {\n      this.#played = 0;\n      this.#seekableRange = new TimeRange(0, 0);\n    }\n    this.#timeRAF.stop();\n    this.#currentTime = 0;\n    this.#reloadInfo = null;\n  }\n  #resumeSession() {\n    const resumeSessionEvent = new DOMEvent(\"resume-session\", { detail: this.session });\n    this.#onMediaLoadedChange(resumeSessionEvent);\n    const { muted, volume, savedState } = this.#ctx.$state, localState = savedState();\n    this.setCurrentTime(Math.max(this.#player.currentTime, localState?.currentTime ?? 0));\n    this.setMuted(muted());\n    this.setVolume(volume());\n    if (localState?.paused === false) this.play();\n  }\n  #endSession() {\n    this.cast.endCurrentSession(true);\n    const { remotePlaybackLoader } = this.#ctx.$state;\n    remotePlaybackLoader.set(null);\n  }\n  #disconnectFromReceiver() {\n    const { savedState } = this.#ctx.$state;\n    savedState.set({\n      paused: this.#player.isPaused,\n      currentTime: this.#player.currentTime\n    });\n    this.#endSession();\n  }\n  #onAnimationFrame() {\n    this.#onCurrentTimeChange();\n  }\n  #onRemotePlayerEvent(event) {\n    this.#playerEventHandlers[event.type].call(this, event);\n  }\n  #onCastStateChange(data) {\n    const castState = this.cast.getCastState(), state = castState === cast.framework.CastState.CONNECTED ? \"connected\" : castState === cast.framework.CastState.CONNECTING ? \"connecting\" : \"disconnected\";\n    if (this.#state === state) return;\n    const detail = { type: \"google-cast\", state }, trigger = this.#createEvent(data);\n    this.#state = state;\n    this.#ctx.notify(\"remote-playback-change\", detail, trigger);\n    if (state === \"disconnected\") {\n      this.#disconnectFromReceiver();\n    }\n  }\n  #onMediaLoadedChange(event) {\n    const hasLoaded = !!this.#player.isMediaLoaded;\n    if (!hasLoaded) return;\n    const src = peek(this.#ctx.$state.source);\n    Promise.resolve().then(() => {\n      if (src !== peek(this.#ctx.$state.source) || !this.#player.isMediaLoaded) return;\n      this.#reset();\n      const duration = this.#player.duration;\n      this.#seekableRange = new TimeRange(0, duration);\n      const detail = {\n        provider: this,\n        duration,\n        buffered: new TimeRange(0, 0),\n        seekable: this.#getSeekableRange()\n      }, trigger = this.#createEvent(event);\n      this.#ctx.notify(\"loaded-metadata\", void 0, trigger);\n      this.#ctx.notify(\"loaded-data\", void 0, trigger);\n      this.#ctx.notify(\"can-play\", detail, trigger);\n      this.#onCanControlVolumeChange();\n      this.#onCanSeekChange(event);\n      const { volume, muted } = this.#ctx.$state;\n      this.setVolume(volume());\n      this.setMuted(muted());\n      this.#timeRAF.start();\n      this.#tracks.syncRemoteTracks(trigger);\n      this.#tracks.syncRemoteActiveIds(trigger);\n    });\n  }\n  #onCanControlVolumeChange() {\n    this.#ctx.$state.canSetVolume.set(this.#player.canControlVolume);\n  }\n  #onCanSeekChange(event) {\n    const trigger = this.#createEvent(event);\n    this.#ctx.notify(\"stream-type-change\", this.#getStreamType(), trigger);\n  }\n  #getStreamType() {\n    const streamType = this.#player.mediaInfo?.streamType;\n    return streamType === chrome.cast.media.StreamType.LIVE ? this.#player.canSeek ? \"live:dvr\" : \"live\" : \"on-demand\";\n  }\n  #onCurrentTimeChange() {\n    if (this.#reloadInfo) return;\n    const currentTime = this.#player.currentTime;\n    if (currentTime === this.#currentTime) return;\n    this.#ctx.notify(\"time-change\", currentTime);\n    if (currentTime > this.#played) {\n      this.#played = currentTime;\n      this.#onProgress();\n    }\n    if (this.#ctx.$state.seeking()) {\n      this.#ctx.notify(\"seeked\", currentTime);\n    }\n    this.#currentTime = currentTime;\n  }\n  #onDurationChange(event) {\n    if (!this.#player.isMediaLoaded || this.#reloadInfo) return;\n    const duration = this.#player.duration, trigger = this.#createEvent(event);\n    this.#seekableRange = new TimeRange(0, duration);\n    this.#ctx.notify(\"duration-change\", duration, trigger);\n  }\n  #onVolumeChange(event) {\n    if (!this.#player.isMediaLoaded) return;\n    const detail = {\n      muted: this.#player.isMuted,\n      volume: this.#player.volumeLevel\n    }, trigger = this.#createEvent(event);\n    this.#ctx.notify(\"volume-change\", detail, trigger);\n  }\n  #onPausedChange(event) {\n    const trigger = this.#createEvent(event);\n    if (this.#player.isPaused) {\n      this.#ctx.notify(\"pause\", void 0, trigger);\n    } else {\n      this.#ctx.notify(\"play\", void 0, trigger);\n    }\n  }\n  #onProgress(event) {\n    const detail = {\n      seekable: this.#getSeekableRange(),\n      buffered: new TimeRange(0, this.#played)\n    }, trigger = event ? this.#createEvent(event) : void 0;\n    this.#ctx.notify(\"progress\", detail, trigger);\n  }\n  #onPlayerStateChange(event) {\n    const state = this.#player.playerState, PlayerState = chrome.cast.media.PlayerState;\n    this.#isIdle = state === PlayerState.IDLE;\n    if (state === PlayerState.PAUSED) return;\n    const trigger = this.#createEvent(event);\n    switch (state) {\n      case PlayerState.PLAYING:\n        this.#ctx.notify(\"playing\", void 0, trigger);\n        break;\n      case PlayerState.BUFFERING:\n        this.#ctx.notify(\"waiting\", void 0, trigger);\n        break;\n      case PlayerState.IDLE:\n        this.#timeRAF.stop();\n        this.#ctx.notify(\"pause\");\n        this.#ctx.notify(\"end\");\n        break;\n    }\n  }\n  #getSeekableRange() {\n    return this.#player.liveSeekableRange ? new TimeRange(this.#player.liveSeekableRange.start, this.#player.liveSeekableRange.end) : this.#seekableRange;\n  }\n  #createEvent(detail) {\n    return detail instanceof Event ? detail : new DOMEvent(detail.type, { detail });\n  }\n  #buildMediaInfo(src) {\n    const { streamType, title, poster } = this.#ctx.$state;\n    return new GoogleCastMediaInfoBuilder(src).setMetadata(title(), poster()).setStreamType(streamType()).setTracks(this.#tracks.getLocalTextTracks()).build();\n  }\n  #buildLoadRequest(src) {\n    const mediaInfo = this.#buildMediaInfo(src), request = new chrome.cast.media.LoadRequest(mediaInfo), savedState = this.#ctx.$state.savedState();\n    request.autoplay = (this.#reloadInfo?.paused ?? savedState?.paused) === false;\n    request.currentTime = this.#reloadInfo?.time ?? savedState?.currentTime ?? 0;\n    return request;\n  }\n  async #reload(paused, time) {\n    const src = peek(this.#ctx.$state.source);\n    this.#reloadInfo = { src, paused, time };\n    await this.loadSource(src);\n  }\n  #onNewLocalTracks() {\n    this.#reload(this.#player.isPaused, this.#player.currentTime).catch((error) => {\n      {\n        this.#ctx.logger?.errorGroup(\"[vidstack] cast failed to load new local tracks\").labelledLog(\"Error\", error).dispatch();\n      }\n    });\n  }\n}\n\nexport { GoogleCastProvider };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAKA,mBAAO;AALP;AAQA,IAAM,6BAAN,MAAiC;AAAA,EAE/B,YAAY,KAAK;AAFnB;AACE;AAEE,uBAAK,OAAQ,IAAI,OAAO,KAAK,MAAM,UAAU,IAAI,KAAK,IAAI,IAAI;AAAA,EAChE;AAAA,EACA,QAAQ;AACN,WAAO,mBAAK;AAAA,EACd;AAAA,EACA,cAAc,YAAY;AACxB,QAAI,WAAW,SAAS,MAAM,GAAG;AAC/B,yBAAK,OAAM,aAAa,OAAO,KAAK,MAAM,WAAW;AAAA,IACvD,OAAO;AACL,yBAAK,OAAM,aAAa,OAAO,KAAK,MAAM,WAAW;AAAA,IACvD;AACA,WAAO;AAAA,EACT;AAAA,EACA,UAAU,QAAQ;AAChB,uBAAK,OAAM,SAAS,OAAO,IAAI,sBAAK,yDAAe;AACnD,WAAO;AAAA,EACT;AAAA,EACA,YAAY,OAAO,QAAQ;AACzB,uBAAK,OAAM,WAAW,IAAI,OAAO,KAAK,MAAM,qBAAqB;AACjE,uBAAK,OAAM,SAAS,QAAQ;AAC5B,uBAAK,OAAM,SAAS,SAAS,CAAC,EAAE,KAAK,OAAO,CAAC;AAC7C,WAAO;AAAA,EACT;AAUF;AAlCE;AADF;AA0BE,oBAAe,SAAC,OAAO,SAAS;AAC9B,QAAM,YAAY,IAAI,OAAO,KAAK,MAAM,MAAM,SAAS,OAAO,KAAK,MAAM,UAAU,IAAI;AACvF,YAAU,OAAO,MAAM;AACvB,YAAU,iBAAiB,MAAM;AACjC,YAAU,mBAAmB;AAC7B,YAAU,WAAW,MAAM;AAC3B,YAAU,UAAU,MAAM,KAAK,YAAY;AAC3C,SAAO;AACT;AA1CF;AA6CA,IAAM,0BAAN,MAA8B;AAAA,EAI5B,YAAYA,OAAM,KAAK,kBAAkB;AAJ3C;AACE;AACA;AACA;AAEE,uBAAK,OAAQA;AACb,uBAAK,MAAO;AACZ,uBAAK,mBAAoB;AAAA,EAC3B;AAAA,EACA,QAAQ;AACN,UAAM,sBAAsB,KAAK,oBAAoB,KAAK,IAAI;AAC9D,gBAAY,mBAAK,MAAK,aAAa,UAAU,mBAAmB;AAChE,gBAAY,mBAAK,MAAK,YAAY,eAAe,mBAAmB;AACpE,WAAO,sBAAK,wDAAiB,KAAK,IAAI,CAAC;AAAA,EACzC;AAAA,EACA,qBAAqB;AACnB,WAAO,mBAAK,MAAK,OAAO,WAAW,EAAE,OAAO,CAAC,UAAU,MAAM,OAAO,MAAM,SAAS,KAAK;AAAA,EAC1F;AAAA,EAqCA,iBAAiB,OAAO;AACtB,QAAI,CAAC,mBAAK,OAAM,cAAe;AAC/B,UAAM,mBAAmB,sBAAK,4DAAL,YAA6B,kBAAkB,KAAK,mBAAmB,GAAG,oBAAoB,sBAAK,wDAAL,WAAsB,OAAO,KAAK,MAAM,UAAU,QAAQ,mBAAmB,sBAAK,wDAAL,WAAsB,OAAO,KAAK,MAAM,UAAU;AACtP,eAAW,oBAAoB,mBAAmB;AAChD,YAAM,gBAAgB,sBAAK,uDAAL,WAAqB,kBAAkB;AAC7D,UAAI,cAAe;AACnB,YAAM,kBAAkB;AAAA,QACtB,IAAI,iBAAiB,QAAQ,SAAS;AAAA,QACtC,OAAO,iBAAiB;AAAA,QACxB,UAAU,iBAAiB;AAAA,QAC3B,MAAM,iBAAiB,WAAW;AAAA,QAClC,UAAU;AAAA,MACZ;AACA,yBAAK,MAAK,YAAY,WAAW,GAAG,EAAE,iBAAiB,KAAK;AAAA,IAC9D;AACA,eAAW,mBAAmB,kBAAkB;AAC9C,YAAM,gBAAgB,sBAAK,uDAAL,WAAqB,iBAAiB;AAC5D,UAAI,cAAe;AACnB,YAAM,iBAAiB;AAAA,QACrB,IAAI,gBAAgB,QAAQ,SAAS;AAAA,QACrC,KAAK,gBAAgB;AAAA,QACrB,OAAO,gBAAgB;AAAA,QACvB,UAAU,gBAAgB;AAAA,QAC1B,MAAM,gBAAgB,QAAQ,YAAY;AAAA,MAC5C;AACA,yBAAK,MAAK,WAAW,IAAI,gBAAgB,KAAK;AAAA,IAChD;AAAA,EACF;AAAA,EACA,oBAAoB,OAAO;AACzB,QAAI,CAAC,mBAAK,OAAM,cAAe;AAC/B,UAAM,YAAY,sBAAK,2DAAL,YAA4B,cAAc,IAAI,OAAO,KAAK,MAAM,sBAAsB,SAAS;AACjH,0BAAK,uDAAL,WAAqB,aAAa,MAAM,CAAC,UAAU;AAlIvD;AAmIM;AACE,iCAAK,MAAK,WAAV,mBAAkB,WAAW,8CAA8C,YAAY,gBAAgB,aAAa,YAAY,SAAS,OAAO;AAAA,MAClJ;AAAA,IACF,CAAC;AAAA,EACH;AAgBF;AAzGE;AACA;AACA;AAHF;AAkBE,yBAAoB,WAAG;AACrB,SAAO,mBAAK,MAAK,OAAO,YAAY;AACtC;AACA,qBAAgB,SAAC,MAAM;AAlEzB;AAmEI,QAAM,WAAS,wBAAK,OAAM,cAAX,mBAAsB,WAAU,CAAC;AAChD,SAAO,OAAO,OAAO,OAAO,CAAC,UAAU,MAAM,SAAS,IAAI,IAAI;AAChE;AACA,wBAAmB,WAAG;AACpB,QAAM,YAAY,CAAC,GAAG,wBAAwB,sBAAK,4DAAL,WAA4B,KAAK,CAAC,UAAU,MAAM,QAAQ,GAAG,wBAAwB,KAAK,mBAAmB,EAAE,OAAO,CAAC,UAAU,MAAM,SAAS,SAAS;AACvM,MAAI,uBAAuB;AACzB,UAAM,oBAAoB,sBAAK,wDAAL,WAAsB,OAAO,KAAK,MAAM,UAAU,QAAQ,mBAAmB,sBAAK,wDAAL,WAAsB,mBAAmB;AAChJ,QAAI,iBAAkB,WAAU,KAAK,iBAAiB,OAAO;AAAA,EAC/D;AACA,MAAI,+DAAuB,QAAQ;AACjC,UAAM,mBAAmB,sBAAK,wDAAL,WAAsB,OAAO,KAAK,MAAM,UAAU;AAC3E,QAAI,iBAAiB,QAAQ;AAC3B,iBAAW,cAAc,uBAAuB;AAC9C,cAAM,kBAAkB,sBAAK,wDAAL,WAAsB,kBAAkB;AAChE,YAAI,gBAAiB,WAAU,KAAK,gBAAgB,OAAO;AAAA,MAC7D;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AACA,qBAAgB,WAAG;AACjB,QAAM,kBAAkB,KAAK,mBAAmB;AAChD,MAAI,CAAC,mBAAK,OAAM,cAAe;AAC/B,QAAM,mBAAmB,sBAAK,wDAAL,WAAsB,OAAO,KAAK,MAAM,UAAU;AAC3E,aAAW,cAAc,iBAAiB;AACxC,UAAM,iBAAiB,sBAAK,wDAAL,WAAsB,kBAAkB;AAC/D,QAAI,CAAC,gBAAgB;AACnB,cAAQ,MAAG;AA9FnB;AA8FsB,wCAAK,uBAAL;AAAA,OAA0B;AACxC;AAAA,IACF;AAAA,EACF;AACF;AAsCA,oBAAe,SAAC,SAAS;AACvB,QAAM,QAAQ,oBAAoB;AAClC,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW,+BAAO,eAAe,SAAS,SAAS,OAAO;AACzF;AACA,oBAAe,SAAC,aAAa,aAAa;AACxC,SAAO,YAAY,KAAK,CAAC,eAAe,sBAAK,gDAAL,WAAc,YAAY,YAAY;AAChF;AACA,qBAAgB,SAAC,cAAc,YAAY;AACzC,SAAO,aAAa,KAAK,CAAC,gBAAgB,sBAAK,gDAAL,WAAc,YAAY,YAAY;AAClF;AAAA;AAAA;AAGA,aAAQ,SAAC,YAAY,aAAa;AAChC,SAAO,YAAY,SAAS,WAAW,SAAS,YAAY,aAAa,WAAW,YAAY,YAAY,QAAQ,YAAY,MAAM,WAAW,KAAK,YAAY;AACpK;AAtJF,aAAAC,OAAA;AAyJA,IAAM,qBAAN,MAAyB;AAAA,EAevB,YAAY,QAAQ,KAAK;AAf3B;AACE,2CAAkB;AAClB,iCAAQ,YAAY;AACpB;AACA,uBAAAA;AACA;AACA,oCAAc;AACd,+BAAS;AACT,qCAAe;AACf,gCAAU;AACV,uCAAiB,IAAI,UAAU,GAAG,CAAC;AACnC,iCAAW,IAAI,QAAQ,sBAAK,oDAAkB,KAAK,IAAI,CAAC;AACxD;AACA,oCAAc;AACd,gCAAU;AAER,uBAAK,SAAU;AACf,uBAAKA,OAAO;AACZ,uBAAK,SAAU,IAAI,wBAAwB,QAAQ,KAAK,sBAAK,oDAAkB,KAAK,IAAI,CAAC;AAAA,EAC3F;AAAA,EACA,IAAI,OAAO;AACT,WAAO;AAAA,EACT;AAAA,EACA,IAAI,aAAa;AACf,WAAO,mBAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,SAAS;AACX,WAAO,mBAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,OAAO;AACT,WAAO,eAAe;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,UAAU;AACZ,WAAO,eAAe;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,QAAQ;AACV,WAAO,oBAAoB;AAAA,EAC7B;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,mBAAmB;AACrB,WAAO,qBAAqB,mBAAK,YAAW;AAAA,EAC9C;AAAA,EACA,QAAQ;AACN,0BAAK,mEAAL;AACA,0BAAK,kEAAL;AACA,uBAAK,SAAQ,MAAM;AACnB,uBAAKA,OAAK,OAAO,kBAAkB,IAAI;AAAA,EACzC;AAAA,EA+BA,MAAM,OAAO;AAvPf;AAwPI,QAAI,CAAC,mBAAK,SAAQ,YAAY,CAAC,mBAAK,SAAS;AAC7C,QAAI,mBAAK,UAAS;AAChB,YAAM,sBAAK,0CAAL,WAAa,OAAO;AAC1B;AAAA,IACF;AACA,6BAAK,SAAQ,eAAb,mBAAyB;AAAA,EAC3B;AAAA,EACA,MAAM,QAAQ;AA/PhB;AAgQI,QAAI,mBAAK,SAAQ,SAAU;AAC3B,6BAAK,SAAQ,eAAb,mBAAyB;AAAA,EAC3B;AAAA,EACA,eAAe,SAAS;AACtB,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AApQ5C;AAqQM,iBAAK,UAAL,mBAAY,UAAU,SAAS,SAAS;AAAA,IAC1C,CAAC;AAAA,EACH;AAAA,EACA,SAAS,OAAO;AAxQlB;AAyQI,UAAM,aAAa,SAAS,CAAC,mBAAK,SAAQ,WAAW,CAAC,SAAS,mBAAK,SAAQ;AAC5E,QAAI,WAAY,0BAAK,SAAQ,eAAb,mBAAyB;AAAA,EAC3C;AAAA,EACA,eAAe,MAAM;AA5QvB;AA6QI,uBAAK,SAAQ,cAAc;AAC3B,uBAAKA,OAAK,OAAO,WAAW,IAAI;AAChC,6BAAK,SAAQ,eAAb,mBAAyB;AAAA,EAC3B;AAAA,EACA,UAAU,QAAQ;AAjRpB;AAkRI,uBAAK,SAAQ,cAAc;AAC3B,6BAAK,SAAQ,eAAb,mBAAyB;AAAA,EAC3B;AAAA,EACA,MAAM,WAAW,KAAK;AArRxB;AAsRI,UAAI,wBAAK,iBAAL,mBAAkB,SAAQ,IAAK,oBAAK,aAAc;AACtD,QAAI,qBAAqB,GAAG,GAAG;AAC7B,4BAAK,iDAAL;AACA,yBAAK,aAAc;AACnB;AAAA,IACF;AACA,uBAAKA,OAAK,OAAO,YAAY;AAC7B,UAAM,cAAc,sBAAK,oDAAL,WAAuB,MAAM,YAAY,MAAM,KAAK,QAAQ,UAAU,WAAW;AACrG,QAAI,WAAW;AACb,yBAAK,aAAc;AACnB,yBAAKA,OAAK,OAAO,SAAS,MAAM,oBAAoB,SAAS,CAAC,CAAC;AAC/D;AAAA,IACF;AACA,uBAAK,aAAc;AAAA,EACrB;AAAA,EACA,UAAU;AACR,0BAAK,yCAAL;AACA,0BAAK,8CAAL;AAAA,EACF;AAiLF;AA7TE;AACAA,QAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAdF;AAgEE,qCAAgC,WAAG;AACjC;AAAA,IACE,KAAK,UAAU,qBAAqB;AAAA,IACpC,sBAAK,qDAAmB,KAAK,IAAI;AAAA,EACnC;AACF;AACA,oCAA+B,WAAG;AAChC,QAAM,SAAS,KAAK,UAAU,uBAAuB,WAAW;AAAA,IAC9D,CAAC,OAAO,oBAAoB,GAAG,sBAAK;AAAA,IACpC,CAAC,OAAO,uBAAuB,GAAG,sBAAK;AAAA,IACvC,CAAC,OAAO,0BAA0B,GAAG,sBAAK;AAAA,IAC1C,CAAC,OAAO,gBAAgB,GAAG,sBAAK;AAAA,IAChC,CAAC,OAAO,gBAAgB,GAAG,sBAAK;AAAA,IAChC,CAAC,OAAO,gBAAgB,GAAG,sBAAK;AAAA,IAChC,CAAC,OAAO,oBAAoB,GAAG,sBAAK;AAAA,IACpC,CAAC,OAAO,iBAAiB,GAAG,sBAAK;AAAA,IACjC,CAAC,OAAO,2BAA2B,GAAG,sBAAK;AAAA,IAC3C,CAAC,OAAO,oBAAoB,GAAG,sBAAK;AAAA,EACtC;AACA,qBAAK,sBAAuB;AAC5B,QAAM,UAAU,sBAAK,uDAAqB,KAAK,IAAI;AACnD,aAAW,QAAQ,OAAO,QAAQ,GAAG;AACnC,uBAAK,SAAQ,WAAW,iBAAiB,MAAM,OAAO;AAAA,EACxD;AACA,YAAU,MAAM;AACd,eAAW,QAAQ,OAAO,QAAQ,GAAG;AACnC,yBAAK,SAAQ,WAAW,oBAAoB,MAAM,OAAO;AAAA,IAC3D;AAAA,EACF,CAAC;AACH;AAmDA,WAAM,WAAG;AACP,MAAI,CAAC,mBAAK,cAAa;AACrB,uBAAK,SAAU;AACf,uBAAK,gBAAiB,IAAI,UAAU,GAAG,CAAC;AAAA,EAC1C;AACA,qBAAK,UAAS,KAAK;AACnB,qBAAK,cAAe;AACpB,qBAAK,aAAc;AACrB;AACA,mBAAc,WAAG;AACf,QAAM,qBAAqB,IAAI,SAAS,kBAAkB,EAAE,QAAQ,KAAK,QAAQ,CAAC;AAClF,wBAAK,uDAAL,WAA0B;AAC1B,QAAM,EAAE,OAAO,QAAQ,WAAW,IAAI,mBAAKA,OAAK,QAAQ,aAAa,WAAW;AAChF,OAAK,eAAe,KAAK,IAAI,mBAAK,SAAQ,cAAa,yCAAY,gBAAe,CAAC,CAAC;AACpF,OAAK,SAAS,MAAM,CAAC;AACrB,OAAK,UAAU,OAAO,CAAC;AACvB,OAAI,yCAAY,YAAW,MAAO,MAAK,KAAK;AAC9C;AACA,gBAAW,WAAG;AACZ,OAAK,KAAK,kBAAkB,IAAI;AAChC,QAAM,EAAE,qBAAqB,IAAI,mBAAKA,OAAK;AAC3C,uBAAqB,IAAI,IAAI;AAC/B;AACA,4BAAuB,WAAG;AACxB,QAAM,EAAE,WAAW,IAAI,mBAAKA,OAAK;AACjC,aAAW,IAAI;AAAA,IACb,QAAQ,mBAAK,SAAQ;AAAA,IACrB,aAAa,mBAAK,SAAQ;AAAA,EAC5B,CAAC;AACD,wBAAK,8CAAL;AACF;AACA,sBAAiB,WAAG;AAClB,wBAAK,uDAAL;AACF;AACA,yBAAoB,SAAC,OAAO;AAC1B,qBAAK,sBAAqB,MAAM,IAAI,EAAE,KAAK,MAAM,KAAK;AACxD;AACA,uBAAkB,SAAC,MAAM;AACvB,QAAM,YAAY,KAAK,KAAK,aAAa,GAAG,QAAQ,cAAc,KAAK,UAAU,UAAU,YAAY,cAAc,cAAc,KAAK,UAAU,UAAU,aAAa,eAAe;AACxL,MAAI,mBAAK,YAAW,MAAO;AAC3B,QAAM,SAAS,EAAE,MAAM,eAAe,MAAM,GAAG,UAAU,sBAAK,+CAAL,WAAkB;AAC3E,qBAAK,QAAS;AACd,qBAAKA,OAAK,OAAO,0BAA0B,QAAQ,OAAO;AAC1D,MAAI,UAAU,gBAAgB;AAC5B,0BAAK,0DAAL;AAAA,EACF;AACF;AACA,yBAAoB,SAAC,OAAO;AAC1B,QAAM,YAAY,CAAC,CAAC,mBAAK,SAAQ;AACjC,MAAI,CAAC,UAAW;AAChB,QAAM,MAAM,KAAK,mBAAKA,OAAK,OAAO,MAAM;AACxC,UAAQ,QAAQ,EAAE,KAAK,MAAM;AAC3B,QAAI,QAAQ,KAAK,mBAAKA,OAAK,OAAO,MAAM,KAAK,CAAC,mBAAK,SAAQ,cAAe;AAC1E,0BAAK,yCAAL;AACA,UAAM,WAAW,mBAAK,SAAQ;AAC9B,uBAAK,gBAAiB,IAAI,UAAU,GAAG,QAAQ;AAC/C,UAAM,SAAS;AAAA,MACb,UAAU;AAAA,MACV;AAAA,MACA,UAAU,IAAI,UAAU,GAAG,CAAC;AAAA,MAC5B,UAAU,sBAAK,oDAAL;AAAA,IACZ,GAAG,UAAU,sBAAK,+CAAL,WAAkB;AAC/B,uBAAKA,OAAK,OAAO,mBAAmB,QAAQ,OAAO;AACnD,uBAAKA,OAAK,OAAO,eAAe,QAAQ,OAAO;AAC/C,uBAAKA,OAAK,OAAO,YAAY,QAAQ,OAAO;AAC5C,0BAAK,4DAAL;AACA,0BAAK,mDAAL,WAAsB;AACtB,UAAM,EAAE,QAAQ,MAAM,IAAI,mBAAKA,OAAK;AACpC,SAAK,UAAU,OAAO,CAAC;AACvB,SAAK,SAAS,MAAM,CAAC;AACrB,uBAAK,UAAS,MAAM;AACpB,uBAAK,SAAQ,iBAAiB,OAAO;AACrC,uBAAK,SAAQ,oBAAoB,OAAO;AAAA,EAC1C,CAAC;AACH;AACA,8BAAyB,WAAG;AAC1B,qBAAKA,OAAK,OAAO,aAAa,IAAI,mBAAK,SAAQ,gBAAgB;AACjE;AACA,qBAAgB,SAAC,OAAO;AACtB,QAAM,UAAU,sBAAK,+CAAL,WAAkB;AAClC,qBAAKA,OAAK,OAAO,sBAAsB,sBAAK,iDAAL,YAAuB,OAAO;AACvE;AACA,mBAAc,WAAG;AA3XnB;AA4XI,QAAM,cAAa,wBAAK,SAAQ,cAAb,mBAAwB;AAC3C,SAAO,eAAe,OAAO,KAAK,MAAM,WAAW,OAAO,mBAAK,SAAQ,UAAU,aAAa,SAAS;AACzG;AACA,yBAAoB,WAAG;AACrB,MAAI,mBAAK,aAAa;AACtB,QAAM,cAAc,mBAAK,SAAQ;AACjC,MAAI,gBAAgB,mBAAK,cAAc;AACvC,qBAAKA,OAAK,OAAO,eAAe,WAAW;AAC3C,MAAI,cAAc,mBAAK,UAAS;AAC9B,uBAAK,SAAU;AACf,0BAAK,8CAAL;AAAA,EACF;AACA,MAAI,mBAAKA,OAAK,OAAO,QAAQ,GAAG;AAC9B,uBAAKA,OAAK,OAAO,UAAU,WAAW;AAAA,EACxC;AACA,qBAAK,cAAe;AACtB;AACA,sBAAiB,SAAC,OAAO;AACvB,MAAI,CAAC,mBAAK,SAAQ,iBAAiB,mBAAK,aAAa;AACrD,QAAM,WAAW,mBAAK,SAAQ,UAAU,UAAU,sBAAK,+CAAL,WAAkB;AACpE,qBAAK,gBAAiB,IAAI,UAAU,GAAG,QAAQ;AAC/C,qBAAKA,OAAK,OAAO,mBAAmB,UAAU,OAAO;AACvD;AACA,oBAAe,SAAC,OAAO;AACrB,MAAI,CAAC,mBAAK,SAAQ,cAAe;AACjC,QAAM,SAAS;AAAA,IACb,OAAO,mBAAK,SAAQ;AAAA,IACpB,QAAQ,mBAAK,SAAQ;AAAA,EACvB,GAAG,UAAU,sBAAK,+CAAL,WAAkB;AAC/B,qBAAKA,OAAK,OAAO,iBAAiB,QAAQ,OAAO;AACnD;AACA,oBAAe,SAAC,OAAO;AACrB,QAAM,UAAU,sBAAK,+CAAL,WAAkB;AAClC,MAAI,mBAAK,SAAQ,UAAU;AACzB,uBAAKA,OAAK,OAAO,SAAS,QAAQ,OAAO;AAAA,EAC3C,OAAO;AACL,uBAAKA,OAAK,OAAO,QAAQ,QAAQ,OAAO;AAAA,EAC1C;AACF;AACA,gBAAW,SAAC,OAAO;AACjB,QAAM,SAAS;AAAA,IACb,UAAU,sBAAK,oDAAL;AAAA,IACV,UAAU,IAAI,UAAU,GAAG,mBAAK,QAAO;AAAA,EACzC,GAAG,UAAU,QAAQ,sBAAK,+CAAL,WAAkB,SAAS;AAChD,qBAAKA,OAAK,OAAO,YAAY,QAAQ,OAAO;AAC9C;AACA,yBAAoB,SAAC,OAAO;AAC1B,QAAM,QAAQ,mBAAK,SAAQ,aAAa,cAAc,OAAO,KAAK,MAAM;AACxE,qBAAK,SAAU,UAAU,YAAY;AACrC,MAAI,UAAU,YAAY,OAAQ;AAClC,QAAM,UAAU,sBAAK,+CAAL,WAAkB;AAClC,UAAQ,OAAO;AAAA,IACb,KAAK,YAAY;AACf,yBAAKA,OAAK,OAAO,WAAW,QAAQ,OAAO;AAC3C;AAAA,IACF,KAAK,YAAY;AACf,yBAAKA,OAAK,OAAO,WAAW,QAAQ,OAAO;AAC3C;AAAA,IACF,KAAK,YAAY;AACf,yBAAK,UAAS,KAAK;AACnB,yBAAKA,OAAK,OAAO,OAAO;AACxB,yBAAKA,OAAK,OAAO,KAAK;AACtB;AAAA,EACJ;AACF;AACA,sBAAiB,WAAG;AAClB,SAAO,mBAAK,SAAQ,oBAAoB,IAAI,UAAU,mBAAK,SAAQ,kBAAkB,OAAO,mBAAK,SAAQ,kBAAkB,GAAG,IAAI,mBAAK;AACzI;AACA,iBAAY,SAAC,QAAQ;AACnB,SAAO,kBAAkB,QAAQ,SAAS,IAAI,SAAS,OAAO,MAAM,EAAE,OAAO,CAAC;AAChF;AACA,oBAAe,SAAC,KAAK;AACnB,QAAM,EAAE,YAAY,OAAO,OAAO,IAAI,mBAAKA,OAAK;AAChD,SAAO,IAAI,2BAA2B,GAAG,EAAE,YAAY,MAAM,GAAG,OAAO,CAAC,EAAE,cAAc,WAAW,CAAC,EAAE,UAAU,mBAAK,SAAQ,mBAAmB,CAAC,EAAE,MAAM;AAC3J;AACA,sBAAiB,SAAC,KAAK;AAvczB;AAwcI,QAAM,YAAY,sBAAK,kDAAL,WAAqB,MAAM,UAAU,IAAI,OAAO,KAAK,MAAM,YAAY,SAAS,GAAG,aAAa,mBAAKA,OAAK,OAAO,WAAW;AAC9I,UAAQ,cAAY,wBAAK,iBAAL,mBAAkB,YAAU,yCAAY,aAAY;AACxE,UAAQ,gBAAc,wBAAK,iBAAL,mBAAkB,UAAQ,yCAAY,gBAAe;AAC3E,SAAO;AACT;AACM,YAAO,eAAC,QAAQ,MAAM;AAC1B,QAAM,MAAM,KAAK,mBAAKA,OAAK,OAAO,MAAM;AACxC,qBAAK,aAAc,EAAE,KAAK,QAAQ,KAAK;AACvC,QAAM,KAAK,WAAW,GAAG;AAC3B;AACA,sBAAiB,WAAG;AAClB,wBAAK,0CAAL,WAAa,mBAAK,SAAQ,UAAU,mBAAK,SAAQ,aAAa,MAAM,CAAC,UAAU;AAndnF;AAodM;AACE,+BAAKA,OAAK,WAAV,mBAAkB,WAAW,mDAAmD,YAAY,SAAS,OAAO;AAAA,IAC9G;AAAA,EACF,CAAC;AACH;", "names": ["cast", "_ctx"]}