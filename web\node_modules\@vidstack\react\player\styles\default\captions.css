/*
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 * Captions
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 */

:where(.vds-captions) {
  /* Recommended settings in the WebVTT spec (https://www.w3.org/TR/webvtt1). */
  --overlay-padding: var(--media-captions-padding, 1%);
  --cue-color: var(--media-user-text-color, var(--media-cue-color, white));
  --cue-bg-color: var(--media-user-text-bg, var(--media-cue-bg, rgba(0, 0, 0, 0.7)));
  --cue-default-font-size: var(--media-cue-font-size, calc(var(--overlay-height) / 100 * 4.5));
  --cue-font-size: calc(var(--cue-default-font-size) * var(--media-user-font-size, 1));
  --cue-line-height: var(--media-cue-line-height, calc(var(--cue-font-size) * 1.2));
  --cue-padding-x: var(--media-cue-padding-x, calc(var(--cue-font-size) * 0.6));
  --cue-padding-y: var(--media-cue-padding-x, calc(var(--cue-font-size) * 0.4));
  --cue-padding: var(--cue-padding-y) var(--cue-padding-x);
  position: absolute;
  inset: 0;
  z-index: 1;
  contain: layout style;
  margin: var(--overlay-padding);
  font-size: var(--cue-font-size);
  font-family: var(--media-user-font-family, sans-serif);
  box-sizing: border-box;
  pointer-events: none;
  user-select: none;
  word-spacing: normal;
  word-break: break-word;
}

:where([data-fullscreen][data-orientation='portrait'] .vds-captions) {
  --cue-default-font-size: var(--media-cue-font-size, calc(var(--overlay-width) / 100 * 4.5));
}

:where([data-view-type='audio'] .vds-captions) {
  position: relative;
  margin: 0;
}

:where(.vds-captions[aria-hidden='true']) {
  opacity: 0;
  visibility: hidden;
}

.vds-captions[data-example] {
  opacity: 1 !important;
  visibility: visible !important;
}

:where([data-view-type='video'] .vds-captions [data-part='cue-display'][data-example]) {
  --cue-text-align: center;
  --cue-width: 100%;
  --cue-top: 90%;
  --cue-left: 0%;
}

:where([data-view-type='audio'] .vds-captions [data-part='cue-display']) {
  --cue-width: 100%;
  position: relative !important;
}

/*
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 * VTT Cues
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 */

:where(.vds-captions [data-part='cue-display']) {
  position: absolute;
  direction: ltr;
  overflow: visible;
  contain: content;
  top: var(--cue-top);
  left: var(--cue-left);
  right: var(--cue-right);
  bottom: var(--cue-bottom);
  width: var(--cue-width, auto);
  height: var(--cue-height, auto);
  box-sizing: border-box;
  transform: var(--cue-transform);
  text-align: var(--cue-text-align);
  writing-mode: var(--cue-writing-mode, unset);
  white-space: pre-line;
  unicode-bidi: plaintext;
  min-width: min-content;
  min-height: min-content;
  background-color: var(--media-user-display-bg, var(--media-cue-display-bg));
  border-radius: var(--media-cue-display-border-radius);
}

.vds-captions [data-part='cue-display'] {
  padding: var(--media-cue-display-padding);
}

:where(.vds-captions[data-dir='rtl'] [data-part='cue-display']) {
  direction: rtl;
}

:where(.vds-captions [data-part='cue']) {
  display: inline-block;
  contain: content;
  font-variant: var(--media-user-font-variant);
  border: var(--media-cue-border, unset);
  border-radius: var(--media-cue-border-radius, 2px);
  backdrop-filter: var(--media-cue-backdrop, blur(8px));
  line-height: var(--cue-line-height);
  box-sizing: border-box;
  box-shadow: var(--media-cue-box-shadow, var(--cue-box-shadow));
  white-space: var(--cue-white-space, pre-wrap);
  outline: var(--cue-outline);
  text-shadow: var(--media-user-text-shadow, var(--cue-text-shadow));
}

.vds-captions [data-part='cue'] {
  background-color: var(--cue-bg-color);
  color: var(--cue-color);
  padding: var(--cue-padding);
}

:where(.vds-captions [data-part='cue-display'][data-vertical] [data-part='cue']) {
  --cue-padding: var(--cue-padding-x) var(--cue-padding-y);
}

/*
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 * VTT Regions
 * ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
 */

:where(.vds-captions [data-part='region']) {
  --anchor-x-percent: calc(var(--region-anchor-x) / 100);
  --anchor-x: calc(var(--region-width) * var(--anchor-x-percent));
  --anchor-y-percent: calc(var(--region-anchor-y) / 100);
  --anchor-y: calc(var(--region-height) * var(--anchor-y-percent));
  --vp-anchor-x: calc(var(--region-viewport-anchor-x) * 1%);
  --vp-anchor-y-percent: calc(var(--region-viewport-anchor-y) / 100);
  --vp-anchor-y: calc(var(--overlay-height) * var(--vp-anchor-y-percent));
  position: absolute;
  display: inline-flex;
  flex-flow: column;
  justify-content: flex-start;
  width: var(--region-width);
  height: var(--region-height);
  min-height: 0px;
  max-height: var(--region-height);
  writing-mode: horizontal-tb;
  top: var(--region-top, calc(var(--vp-anchor-y) - var(--anchor-y)));
  left: var(--region-left, calc(var(--vp-anchor-x) - var(--anchor-x)));
  right: var(--region-right);
  bottom: var(--region-bottom);
  overflow: hidden;
  overflow-wrap: break-word;
  box-sizing: border-box;
}

:where(.vds-captions [data-part='region'][data-active]) {
}

:where(.vds-captions [data-part='region'][data-scroll='up']) {
  justify-content: end;
}

:where(.vds-captions [data-part='region'][data-active][data-scroll='up']) {
  transition: top 0.433s;
}

:where(.vds-captions [data-part='region'] > [data-part='cue-display']) {
  position: relative;
  width: auto;
  left: var(--cue-offset);
  height: var(--cue-height, auto);
  text-align: var(--cue-text-align);
  unicode-bidi: plaintext;
  margin-top: 2px;
}

:where(.vds-captions [data-part='region'] [data-part='cue']) {
  position: relative;
  border-radius: 0px;
}
