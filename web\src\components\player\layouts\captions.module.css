/**
 * !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
 * You can also import the same styles from "@vidstack/react/player/styles/default/captions.css"
 *
 * <Captions className="vds-captions" />
 * !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
 */

.captions {
  /* Enhanced subtitle styling for better readability */
  --cue-color: var(--media-cue-color, #ffffff);
  --cue-bg-color: var(--media-cue-bg, rgba(0, 0, 0, 0.85));
  --cue-font-size: calc(var(--overlay-height) / 100 * 4.2);
  --cue-line-height: calc(var(--cue-font-size) * 1.3);
  --cue-padding-x: calc(var(--cue-font-size) * 0.8);
  --cue-padding-y: calc(var(--cue-font-size) * 0.5);
  --direction: 0;
  --align: center;
  padding: 2%;
  color: #ffffff;
  font-family: 'Inter', 'Segoe UI', 'Roboto', sans-serif;
  text-align: var(--align);
  translate: 0 calc(var(--direction) * -1px);
  font-size: var(--cue-font-size);
  font-weight: 500;
  word-spacing: normal;
  contain: layout style;
  letter-spacing: 0.025em;
}

.captions[data-dir='rtl'] :global([data-part='cue-display']) {
  direction: rtl;
}

.captions[aria-hidden='true'] {
  display: none;
}

/* Force captions to be visible for debugging */
.captions {
  opacity: 1 !important;
  z-index: 1000 !important;
}

.captions[data-visible] {
  opacity: 1 !important;
}

/*************************************************************************************************
 * Cue Display
 *************************************************************************************************/

/*
* Most of the cue styles are set automatically by our [media-captions](https://github.com/vidstack/media-captions)
* library via CSS variables. They are inferred from the VTT, SRT, or SSA file cue settings. You're
* free to ignore them and style the captions as desired, but we don't recommend it unless the
* captions file contains no cue settings. Otherwise, you might be breaking accessibility.
*/
.captions :global([data-part='cue-display']) {
  position: absolute;
  direction: ltr;
  overflow: visible;
  contain: content;
  top: var(--cue-top);
  left: var(--cue-left);
  right: var(--cue-right);
  bottom: var(--cue-bottom);
  width: var(--cue-width, auto);
  height: var(--cue-height, auto);
  transform: var(--cue-transform);
  text-align: var(--cue-text-align);
  writing-mode: var(--cue-writing-mode, unset);
  white-space: pre-line;
  unicode-bidi: plaintext;
  min-width: min-content;
  min-height: min-content;
}

.captions :global([data-part='cue']) {
  display: inline-block;
  contain: content;
  border-radius: 6px;
  backdrop-filter: blur(12px);
  padding: var(--cue-padding-y) var(--cue-padding-x);
  line-height: var(--cue-line-height);
  background-color: var(--cue-bg-color);
  color: var(--cue-color);
  white-space: pre-wrap;
  outline: var(--cue-outline);
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8), 0 0 4px rgba(0, 0, 0, 0.5);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.captions :global([data-part='cue-display'][data-vertical] [data-part='cue']) {
  padding: var(--cue-padding-x) var(--cue-padding-y);
}
