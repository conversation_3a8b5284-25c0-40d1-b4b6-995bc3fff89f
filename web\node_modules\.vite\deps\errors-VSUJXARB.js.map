{"version": 3, "sources": ["../../@vidstack/react/node_modules/media-captions/dist/dev/errors.js"], "sourcesContent": ["import { P as ParseError, c as ParseErrorCode } from './index.js';\n\nconst ParseErrorBuilder = {\n  _badVTTHeader() {\n    return new ParseError({\n      code: ParseErrorCode.BadSignature,\n      reason: \"missing WEBVTT file header\",\n      line: 1\n    });\n  },\n  _badStartTimestamp(startTime, line) {\n    return new ParseError({\n      code: ParseErrorCode.BadTimestamp,\n      reason: `cue start timestamp \\`${startTime}\\` is invalid on line ${line}`,\n      line\n    });\n  },\n  _badEndTimestamp(endTime, line) {\n    return new ParseError({\n      code: ParseErrorCode.BadTimestamp,\n      reason: `cue end timestamp \\`${endTime}\\` is invalid on line ${line}`,\n      line\n    });\n  },\n  _badRangeTimestamp(startTime, endTime, line) {\n    return new ParseError({\n      code: ParseErrorCode.BadTimestamp,\n      reason: `cue end timestamp \\`${endTime}\\` is greater than start \\`${startTime}\\` on line ${line}`,\n      line\n    });\n  },\n  _badCueSetting(name, value, line) {\n    return new ParseError({\n      code: ParseErrorCode.BadSettingValue,\n      reason: `invalid value for cue setting \\`${name}\\` on line ${line} (value: ${value})`,\n      line\n    });\n  },\n  _unknownCueSetting(name, value, line) {\n    return new ParseError({\n      code: ParseErrorCode.UnknownSetting,\n      reason: `unknown cue setting \\`${name}\\` on line ${line} (value: ${value})`,\n      line\n    });\n  },\n  _badRegionSetting(name, value, line) {\n    return new ParseError({\n      code: ParseErrorCode.BadSettingValue,\n      reason: `invalid value for region setting \\`${name}\\` on line ${line} (value: ${value})`,\n      line\n    });\n  },\n  _unknownRegionSetting(name, value, line) {\n    return new ParseError({\n      code: ParseErrorCode.UnknownSetting,\n      reason: `unknown region setting \\`${name}\\` on line ${line} (value: ${value})`,\n      line\n    });\n  },\n  // SSA-specific errors\n  _missingFormat(type, line) {\n    return new ParseError({\n      code: ParseErrorCode.BadFormat,\n      reason: `format missing for \\`${type}\\` block on line ${line}`,\n      line\n    });\n  }\n};\n\nexport { ParseErrorBuilder };\n"], "mappings": ";;;;;;;AAEA,IAAM,oBAAoB;AAAA,EACxB,gBAAgB;AACd,WAAO,IAAI,WAAW;AAAA,MACpB,MAAM,eAAe;AAAA,MACrB,QAAQ;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,EACH;AAAA,EACA,mBAAmB,WAAW,MAAM;AAClC,WAAO,IAAI,WAAW;AAAA,MACpB,MAAM,eAAe;AAAA,MACrB,QAAQ,yBAAyB,SAAS,yBAAyB,IAAI;AAAA,MACvE;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,iBAAiB,SAAS,MAAM;AAC9B,WAAO,IAAI,WAAW;AAAA,MACpB,MAAM,eAAe;AAAA,MACrB,QAAQ,uBAAuB,OAAO,yBAAyB,IAAI;AAAA,MACnE;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,mBAAmB,WAAW,SAAS,MAAM;AAC3C,WAAO,IAAI,WAAW;AAAA,MACpB,MAAM,eAAe;AAAA,MACrB,QAAQ,uBAAuB,OAAO,8BAA8B,SAAS,cAAc,IAAI;AAAA,MAC/F;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,eAAe,MAAM,OAAO,MAAM;AAChC,WAAO,IAAI,WAAW;AAAA,MACpB,MAAM,eAAe;AAAA,MACrB,QAAQ,mCAAmC,IAAI,cAAc,IAAI,YAAY,KAAK;AAAA,MAClF;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,mBAAmB,MAAM,OAAO,MAAM;AACpC,WAAO,IAAI,WAAW;AAAA,MACpB,MAAM,eAAe;AAAA,MACrB,QAAQ,yBAAyB,IAAI,cAAc,IAAI,YAAY,KAAK;AAAA,MACxE;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,kBAAkB,MAAM,OAAO,MAAM;AACnC,WAAO,IAAI,WAAW;AAAA,MACpB,MAAM,eAAe;AAAA,MACrB,QAAQ,sCAAsC,IAAI,cAAc,IAAI,YAAY,KAAK;AAAA,MACrF;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,sBAAsB,MAAM,OAAO,MAAM;AACvC,WAAO,IAAI,WAAW;AAAA,MACpB,MAAM,eAAe;AAAA,MACrB,QAAQ,4BAA4B,IAAI,cAAc,IAAI,YAAY,KAAK;AAAA,MAC3E;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,eAAe,MAAM,MAAM;AACzB,WAAO,IAAI,WAAW;AAAA,MACpB,MAAM,eAAe;AAAA,MACrB,QAAQ,wBAAwB,IAAI,oBAAoB,IAAI;AAAA,MAC5D;AAAA,IACF,CAAC;AAAA,EACH;AACF;", "names": []}