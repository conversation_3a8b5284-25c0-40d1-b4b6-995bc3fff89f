{"version": 3, "sources": ["../../@vidstack/react/dev/chunks/vidstack-DF6waG6B.js"], "sourcesContent": ["\"use client\"\n\nimport { createScope, signal, effect, peek, isString, deferredPromise, listenEvent, isArray } from './vidstack-CH225ns1.js';\nimport { TimeRange, RAFLoop, preconnect, ListSymbol, TextTrack, QualitySymbol } from './vidstack-C-WrcxmD.js';\nimport { EmbedProvider } from './vidstack-CFWvJYI8.js';\nimport { resolveVimeoVideoId, getVimeoVideoInfo } from './vidstack-krOAtKMi.js';\nimport 'react';\nimport '@floating-ui/dom';\n\nconst trackedVimeoEvents = [\n  \"bufferend\",\n  \"bufferstart\",\n  // 'cuechange',\n  \"durationchange\",\n  \"ended\",\n  \"enterpictureinpicture\",\n  \"error\",\n  \"fullscreenchange\",\n  \"leavepictureinpicture\",\n  \"loaded\",\n  // 'loadeddata',\n  // 'loadedmetadata',\n  // 'loadstart',\n  \"playProgress\",\n  \"loadProgress\",\n  \"pause\",\n  \"play\",\n  \"playbackratechange\",\n  // 'progress',\n  \"qualitychange\",\n  \"seeked\",\n  \"seeking\",\n  // 'texttrackchange',\n  \"timeupdate\",\n  \"volumechange\",\n  \"waiting\"\n  // 'adstarted',\n  // 'adcompleted',\n  // 'aderror',\n  // 'adskipped',\n  // 'adallcompleted',\n  // 'adclicked',\n  // 'chapterchange',\n  // 'chromecastconnected',\n  // 'remoteplaybackavailabilitychange',\n  // 'remoteplaybackconnecting',\n  // 'remoteplaybackconnect',\n  // 'remoteplaybackdisconnect',\n  // 'liveeventended',\n  // 'liveeventstarted',\n  // 'livestreamoffline',\n  // 'livestreamonline',\n];\n\nclass VimeoProvider extends EmbedProvider {\n  $$PROVIDER_TYPE = \"VIMEO\";\n  scope = createScope();\n  fullscreen;\n  #ctx;\n  #videoId = signal(\"\");\n  #pro = signal(false);\n  #hash = null;\n  #currentSrc = null;\n  #fullscreenActive = false;\n  #seekableRange = new TimeRange(0, 0);\n  #timeRAF = new RAFLoop(this.#onAnimationFrame.bind(this));\n  #currentCue = null;\n  #chaptersTrack = null;\n  #promises = /* @__PURE__ */ new Map();\n  #videoInfoPromise = null;\n  constructor(iframe, ctx) {\n    super(iframe);\n    this.#ctx = ctx;\n    const self = this;\n    this.fullscreen = {\n      get active() {\n        return self.#fullscreenActive;\n      },\n      supported: true,\n      enter: () => this.#remote(\"requestFullscreen\"),\n      exit: () => this.#remote(\"exitFullscreen\")\n    };\n  }\n  /**\n   * Whether tracking session data should be enabled on the embed, including cookies and analytics.\n   * This is turned off by default to be GDPR-compliant.\n   *\n   * @defaultValue `false`\n   */\n  cookies = false;\n  title = true;\n  byline = true;\n  portrait = true;\n  color = \"00ADEF\";\n  get type() {\n    return \"vimeo\";\n  }\n  get currentSrc() {\n    return this.#currentSrc;\n  }\n  get videoId() {\n    return this.#videoId();\n  }\n  get hash() {\n    return this.#hash;\n  }\n  get isPro() {\n    return this.#pro();\n  }\n  preconnect() {\n    preconnect(this.getOrigin());\n  }\n  setup() {\n    super.setup();\n    effect(this.#watchVideoId.bind(this));\n    effect(this.#watchVideoInfo.bind(this));\n    effect(this.#watchPro.bind(this));\n    this.#ctx.notify(\"provider-setup\", this);\n  }\n  destroy() {\n    this.#reset();\n    this.fullscreen = void 0;\n    const message = \"provider destroyed\";\n    for (const promises of this.#promises.values()) {\n      for (const { reject } of promises) reject(message);\n    }\n    this.#promises.clear();\n    this.#remote(\"destroy\");\n  }\n  async play() {\n    return this.#remote(\"play\");\n  }\n  async pause() {\n    return this.#remote(\"pause\");\n  }\n  setMuted(muted) {\n    this.#remote(\"setMuted\", muted);\n  }\n  setCurrentTime(time) {\n    this.#remote(\"seekTo\", time);\n    this.#ctx.notify(\"seeking\", time);\n  }\n  setVolume(volume) {\n    this.#remote(\"setVolume\", volume);\n    this.#remote(\"setMuted\", peek(this.#ctx.$state.muted));\n  }\n  setPlaybackRate(rate) {\n    this.#remote(\"setPlaybackRate\", rate);\n  }\n  async loadSource(src) {\n    if (!isString(src.src)) {\n      this.#currentSrc = null;\n      this.#hash = null;\n      this.#videoId.set(\"\");\n      return;\n    }\n    const { videoId, hash } = resolveVimeoVideoId(src.src);\n    this.#videoId.set(videoId ?? \"\");\n    this.#hash = hash ?? null;\n    this.#currentSrc = src;\n  }\n  #watchVideoId() {\n    this.#reset();\n    const videoId = this.#videoId();\n    if (!videoId) {\n      this.src.set(\"\");\n      return;\n    }\n    this.src.set(`${this.getOrigin()}/video/${videoId}`);\n    this.#ctx.notify(\"load-start\");\n  }\n  #watchVideoInfo() {\n    const videoId = this.#videoId();\n    if (!videoId) return;\n    const promise = deferredPromise(), abort = new AbortController();\n    this.#videoInfoPromise = promise;\n    getVimeoVideoInfo(videoId, abort, this.#hash).then((info) => {\n      promise.resolve(info);\n    }).catch((e) => {\n      promise.reject();\n      {\n        this.#ctx.logger?.warnGroup(`Failed to fetch vimeo video info for id \\`${videoId}\\`.`).labelledLog(\"Error\", e).dispatch();\n      }\n    });\n    return () => {\n      promise.reject();\n      abort.abort();\n    };\n  }\n  #watchPro() {\n    const isPro = this.#pro(), { $state, qualities } = this.#ctx;\n    $state.canSetPlaybackRate.set(isPro);\n    qualities[ListSymbol.setReadonly](!isPro);\n    if (isPro) {\n      return listenEvent(qualities, \"change\", () => {\n        if (qualities.auto) return;\n        const id = qualities.selected?.id;\n        if (id) this.#remote(\"setQuality\", id);\n      });\n    }\n  }\n  getOrigin() {\n    return \"https://player.vimeo.com\";\n  }\n  buildParams() {\n    const { keyDisabled } = this.#ctx.$props, { playsInline, nativeControls } = this.#ctx.$state, showControls = nativeControls();\n    return {\n      title: this.title,\n      byline: this.byline,\n      color: this.color,\n      portrait: this.portrait,\n      controls: showControls,\n      h: this.hash,\n      keyboard: showControls && !keyDisabled(),\n      transparent: true,\n      playsinline: playsInline(),\n      dnt: !this.cookies\n    };\n  }\n  #onAnimationFrame() {\n    this.#remote(\"getCurrentTime\");\n  }\n  // Embed will sometimes dispatch 0 at end of playback.\n  #preventTimeUpdates = false;\n  #onTimeUpdate(time, trigger) {\n    if (this.#preventTimeUpdates && time === 0) return;\n    const { realCurrentTime, paused, bufferedEnd, seekableEnd, live } = this.#ctx.$state;\n    if (realCurrentTime() === time) return;\n    const prevTime = realCurrentTime();\n    this.#ctx.notify(\"time-change\", time, trigger);\n    if (Math.abs(prevTime - time) > 1.5) {\n      this.#ctx.notify(\"seeking\", time, trigger);\n      if (!paused() && bufferedEnd() < time) {\n        this.#ctx.notify(\"waiting\", void 0, trigger);\n      }\n    }\n    if (!live() && seekableEnd() - time < 0.01) {\n      this.#ctx.notify(\"end\", void 0, trigger);\n      this.#preventTimeUpdates = true;\n      setTimeout(() => {\n        this.#preventTimeUpdates = false;\n      }, 500);\n    }\n  }\n  #onSeeked(time, trigger) {\n    this.#ctx.notify(\"seeked\", time, trigger);\n  }\n  #onLoaded(trigger) {\n    const videoId = this.#videoId();\n    this.#videoInfoPromise?.promise.then((info) => {\n      if (!info) return;\n      const { title, poster, duration, pro } = info;\n      this.#pro.set(pro);\n      this.#ctx.notify(\"title-change\", title, trigger);\n      this.#ctx.notify(\"poster-change\", poster, trigger);\n      this.#ctx.notify(\"duration-change\", duration, trigger);\n      this.#onReady(duration, trigger);\n    }).catch(() => {\n      if (videoId !== this.#videoId()) return;\n      this.#remote(\"getVideoTitle\");\n      this.#remote(\"getDuration\");\n    });\n  }\n  #onReady(duration, trigger) {\n    const { nativeControls } = this.#ctx.$state, showEmbedControls = nativeControls();\n    this.#seekableRange = new TimeRange(0, duration);\n    const detail = {\n      buffered: new TimeRange(0, 0),\n      seekable: this.#seekableRange,\n      duration\n    };\n    this.#ctx.delegate.ready(detail, trigger);\n    if (!showEmbedControls) {\n      this.#remote(\"_hideOverlay\");\n    }\n    this.#remote(\"getQualities\");\n    this.#remote(\"getChapters\");\n  }\n  #onMethod(method, data, trigger) {\n    switch (method) {\n      case \"getVideoTitle\":\n        const videoTitle = data;\n        this.#ctx.notify(\"title-change\", videoTitle, trigger);\n        break;\n      case \"getDuration\":\n        const duration = data;\n        if (!this.#ctx.$state.canPlay()) {\n          this.#onReady(duration, trigger);\n        } else {\n          this.#ctx.notify(\"duration-change\", duration, trigger);\n        }\n        break;\n      case \"getCurrentTime\":\n        this.#onTimeUpdate(data, trigger);\n        break;\n      case \"getBuffered\":\n        if (isArray(data) && data.length) {\n          this.#onLoadProgress(data[data.length - 1][1], trigger);\n        }\n        break;\n      case \"setMuted\":\n        this.#onVolumeChange(peek(this.#ctx.$state.volume), data, trigger);\n        break;\n      // case 'getTextTracks':\n      //   this.#onTextTracksChange(data as VimeoTextTrack[], trigger);\n      //   break;\n      case \"getChapters\":\n        this.#onChaptersChange(data);\n        break;\n      case \"getQualities\":\n        this.#onQualitiesChange(data, trigger);\n        break;\n    }\n    this.#getPromise(method)?.resolve();\n  }\n  #attachListeners() {\n    for (const type of trackedVimeoEvents) {\n      this.#remote(\"addEventListener\", type);\n    }\n  }\n  #onPause(trigger) {\n    this.#timeRAF.stop();\n    this.#ctx.notify(\"pause\", void 0, trigger);\n  }\n  #onPlay(trigger) {\n    this.#timeRAF.start();\n    this.#ctx.notify(\"play\", void 0, trigger);\n  }\n  #onPlayProgress(trigger) {\n    const { paused } = this.#ctx.$state;\n    if (!paused() && !this.#preventTimeUpdates) {\n      this.#ctx.notify(\"playing\", void 0, trigger);\n    }\n  }\n  #onLoadProgress(buffered, trigger) {\n    const detail = {\n      buffered: new TimeRange(0, buffered),\n      seekable: this.#seekableRange\n    };\n    this.#ctx.notify(\"progress\", detail, trigger);\n  }\n  #onBufferStart(trigger) {\n    this.#ctx.notify(\"waiting\", void 0, trigger);\n  }\n  #onBufferEnd(trigger) {\n    const { paused } = this.#ctx.$state;\n    if (!paused()) this.#ctx.notify(\"playing\", void 0, trigger);\n  }\n  #onWaiting(trigger) {\n    const { paused } = this.#ctx.$state;\n    if (paused()) {\n      this.#ctx.notify(\"play\", void 0, trigger);\n    }\n    this.#ctx.notify(\"waiting\", void 0, trigger);\n  }\n  #onVolumeChange(volume, muted, trigger) {\n    const detail = { volume, muted };\n    this.#ctx.notify(\"volume-change\", detail, trigger);\n  }\n  // #onTextTrackChange(track: VimeoTextTrack, trigger: Event) {\n  //   const textTrack = this.#ctx.textTracks.toArray().find((t) => t.language === track.language);\n  //   if (textTrack) textTrack.mode = track.mode;\n  // }\n  // #onTextTracksChange(tracks: VimeoTextTrack[], trigger: Event) {\n  //   for (const init of tracks) {\n  //     const textTrack = new TextTrack({\n  //       ...init,\n  //       label: init.label.replace('auto-generated', 'auto'),\n  //     });\n  //     textTrack[TextTrackSymbol.readyState] = 2;\n  //     this.#ctx.textTracks.add(textTrack, trigger);\n  //     textTrack.setMode(init.mode, trigger);\n  //   }\n  // }\n  // #onCueChange(cue: VimeoTextCue, trigger: Event) {\n  //   const { textTracks, $state } = this.#ctx,\n  //     { currentTime } = $state,\n  //     track = textTracks.selected;\n  //   if (this.#currentCue) track?.removeCue(this.#currentCue, trigger);\n  //   this.#currentCue = new window.VTTCue(currentTime(), Number.MAX_SAFE_INTEGER, cue.text);\n  //   track?.addCue(this.#currentCue, trigger);\n  // }\n  #onChaptersChange(chapters) {\n    this.#removeChapters();\n    if (!chapters.length) return;\n    const track = new TextTrack({\n      kind: \"chapters\",\n      default: true\n    }), { seekableEnd } = this.#ctx.$state;\n    for (let i = 0; i < chapters.length; i++) {\n      const chapter = chapters[i], nextChapter = chapters[i + 1];\n      track.addCue(\n        new window.VTTCue(\n          chapter.startTime,\n          nextChapter?.startTime ?? seekableEnd(),\n          chapter.title\n        )\n      );\n    }\n    this.#chaptersTrack = track;\n    this.#ctx.textTracks.add(track);\n  }\n  #removeChapters() {\n    if (!this.#chaptersTrack) return;\n    this.#ctx.textTracks.remove(this.#chaptersTrack);\n    this.#chaptersTrack = null;\n  }\n  #onQualitiesChange(qualities, trigger) {\n    this.#ctx.qualities[QualitySymbol.enableAuto] = qualities.some((q) => q.id === \"auto\") ? () => this.#remote(\"setQuality\", \"auto\") : void 0;\n    for (const quality of qualities) {\n      if (quality.id === \"auto\") continue;\n      const height = +quality.id.slice(0, -1);\n      if (isNaN(height)) continue;\n      this.#ctx.qualities[ListSymbol.add](\n        {\n          id: quality.id,\n          width: height * (16 / 9),\n          height,\n          codec: \"avc1,h.264\",\n          bitrate: -1\n        },\n        trigger\n      );\n    }\n    this.#onQualityChange(\n      qualities.find((q) => q.active),\n      trigger\n    );\n  }\n  #onQualityChange({ id } = {}, trigger) {\n    if (!id) return;\n    const isAuto = id === \"auto\", newQuality = this.#ctx.qualities.getById(id);\n    if (isAuto) {\n      this.#ctx.qualities[QualitySymbol.setAuto](isAuto, trigger);\n      this.#ctx.qualities[ListSymbol.select](void 0, true, trigger);\n    } else {\n      this.#ctx.qualities[ListSymbol.select](newQuality ?? void 0, true, trigger);\n    }\n  }\n  #onEvent(event, payload, trigger) {\n    switch (event) {\n      case \"ready\":\n        this.#attachListeners();\n        break;\n      case \"loaded\":\n        this.#onLoaded(trigger);\n        break;\n      case \"play\":\n        this.#onPlay(trigger);\n        break;\n      case \"playProgress\":\n        this.#onPlayProgress(trigger);\n        break;\n      case \"pause\":\n        this.#onPause(trigger);\n        break;\n      case \"loadProgress\":\n        this.#onLoadProgress(payload.seconds, trigger);\n        break;\n      case \"waiting\":\n        this.#onWaiting(trigger);\n        break;\n      case \"bufferstart\":\n        this.#onBufferStart(trigger);\n        break;\n      case \"bufferend\":\n        this.#onBufferEnd(trigger);\n        break;\n      case \"volumechange\":\n        this.#onVolumeChange(payload.volume, peek(this.#ctx.$state.muted), trigger);\n        break;\n      case \"durationchange\":\n        this.#seekableRange = new TimeRange(0, payload.duration);\n        this.#ctx.notify(\"duration-change\", payload.duration, trigger);\n        break;\n      case \"playbackratechange\":\n        this.#ctx.notify(\"rate-change\", payload.playbackRate, trigger);\n        break;\n      case \"qualitychange\":\n        this.#onQualityChange(payload, trigger);\n        break;\n      case \"fullscreenchange\":\n        this.#fullscreenActive = payload.fullscreen;\n        this.#ctx.notify(\"fullscreen-change\", payload.fullscreen, trigger);\n        break;\n      case \"enterpictureinpicture\":\n        this.#ctx.notify(\"picture-in-picture-change\", true, trigger);\n        break;\n      case \"leavepictureinpicture\":\n        this.#ctx.notify(\"picture-in-picture-change\", false, trigger);\n        break;\n      case \"ended\":\n        this.#ctx.notify(\"end\", void 0, trigger);\n        break;\n      case \"error\":\n        this.#onError(payload, trigger);\n        break;\n      case \"seek\":\n      case \"seeked\":\n        this.#onSeeked(payload.seconds, trigger);\n        break;\n    }\n  }\n  #onError(error, trigger) {\n    const { message, method } = error;\n    if (method === \"setPlaybackRate\") {\n      this.#pro.set(false);\n    }\n    if (method) {\n      this.#getPromise(method)?.reject(message);\n    }\n    {\n      this.#ctx.logger?.errorGroup(`[vimeo]: ${message}`).labelledLog(\"Error\", error).labelledLog(\"Provider\", this).labelledLog(\"Event\", trigger).dispatch();\n    }\n  }\n  onMessage(message, event) {\n    if (message.event) {\n      this.#onEvent(message.event, message.data, event);\n    } else if (message.method) {\n      this.#onMethod(message.method, message.value, event);\n    }\n  }\n  onLoad() {\n  }\n  async #remote(command, arg) {\n    let promise = deferredPromise(), promises = this.#promises.get(command);\n    if (!promises) this.#promises.set(command, promises = []);\n    promises.push(promise);\n    this.postMessage({\n      method: command,\n      value: arg\n    });\n    return promise.promise;\n  }\n  #reset() {\n    this.#timeRAF.stop();\n    this.#seekableRange = new TimeRange(0, 0);\n    this.#videoInfoPromise = null;\n    this.#currentCue = null;\n    this.#pro.set(false);\n    this.#removeChapters();\n  }\n  #getPromise(command) {\n    return this.#promises.get(command)?.shift();\n  }\n}\n\nexport { VimeoProvider };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMA,mBAAO;AAGP,IAAM,qBAAqB;AAAA,EACzB;AAAA,EACA;AAAA;AAAA,EAEA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAAA;AAAA;AAAA,EAIA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAAA,EAEA;AAAA,EACA;AAAA,EACA;AAAA;AAAA,EAEA;AAAA,EACA;AAAA,EACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAiBF;AApDA;AAsDA,IAAM,gBAAN,cAA4B,cAAc;AAAA,EAgBxC,YAAY,QAAQ,KAAK;AACvB,UAAM,MAAM;AAjBhB;AACE,2CAAkB;AAClB,iCAAQ,YAAY;AACpB;AACA;AACA,iCAAW,OAAO,EAAE;AACpB,6BAAO,OAAO,KAAK;AACnB,8BAAQ;AACR,oCAAc;AACd,0CAAoB;AACpB,uCAAiB,IAAI,UAAU,GAAG,CAAC;AACnC,iCAAW,IAAI,QAAQ,sBAAK,+CAAkB,KAAK,IAAI,CAAC;AACxD,oCAAc;AACd,uCAAiB;AACjB,kCAA4B,oBAAI,IAAI;AACpC,0CAAoB;AAoBpB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mCAAU;AACV,iCAAQ;AACR,kCAAS;AACT,oCAAW;AACX,iCAAQ;AAkIR;AAAA,4CAAsB;AAvJpB,uBAAK,MAAO;AACZ,UAAM,OAAO;AACb,SAAK,aAAa;AAAA,MAChB,IAAI,SAAS;AACX,eAAO,mBAAK;AAAA,MACd;AAAA,MACA,WAAW;AAAA,MACX,OAAO,MAAM,sBAAK,qCAAL,WAAa;AAAA,MAC1B,MAAM,MAAM,sBAAK,qCAAL,WAAa;AAAA,IAC3B;AAAA,EACF;AAAA,EAYA,IAAI,OAAO;AACT,WAAO;AAAA,EACT;AAAA,EACA,IAAI,aAAa;AACf,WAAO,mBAAK;AAAA,EACd;AAAA,EACA,IAAI,UAAU;AACZ,WAAO,mBAAK,UAAL;AAAA,EACT;AAAA,EACA,IAAI,OAAO;AACT,WAAO,mBAAK;AAAA,EACd;AAAA,EACA,IAAI,QAAQ;AACV,WAAO,mBAAK,MAAL;AAAA,EACT;AAAA,EACA,aAAa;AACX,eAAW,KAAK,UAAU,CAAC;AAAA,EAC7B;AAAA,EACA,QAAQ;AACN,UAAM,MAAM;AACZ,WAAO,sBAAK,2CAAc,KAAK,IAAI,CAAC;AACpC,WAAO,sBAAK,6CAAgB,KAAK,IAAI,CAAC;AACtC,WAAO,sBAAK,uCAAU,KAAK,IAAI,CAAC;AAChC,uBAAK,MAAK,OAAO,kBAAkB,IAAI;AAAA,EACzC;AAAA,EACA,UAAU;AACR,0BAAK,oCAAL;AACA,SAAK,aAAa;AAClB,UAAM,UAAU;AAChB,eAAW,YAAY,mBAAK,WAAU,OAAO,GAAG;AAC9C,iBAAW,EAAE,OAAO,KAAK,SAAU,QAAO,OAAO;AAAA,IACnD;AACA,uBAAK,WAAU,MAAM;AACrB,0BAAK,qCAAL,WAAa;AAAA,EACf;AAAA,EACA,MAAM,OAAO;AACX,WAAO,sBAAK,qCAAL,WAAa;AAAA,EACtB;AAAA,EACA,MAAM,QAAQ;AACZ,WAAO,sBAAK,qCAAL,WAAa;AAAA,EACtB;AAAA,EACA,SAAS,OAAO;AACd,0BAAK,qCAAL,WAAa,YAAY;AAAA,EAC3B;AAAA,EACA,eAAe,MAAM;AACnB,0BAAK,qCAAL,WAAa,UAAU;AACvB,uBAAK,MAAK,OAAO,WAAW,IAAI;AAAA,EAClC;AAAA,EACA,UAAU,QAAQ;AAChB,0BAAK,qCAAL,WAAa,aAAa;AAC1B,0BAAK,qCAAL,WAAa,YAAY,KAAK,mBAAK,MAAK,OAAO,KAAK;AAAA,EACtD;AAAA,EACA,gBAAgB,MAAM;AACpB,0BAAK,qCAAL,WAAa,mBAAmB;AAAA,EAClC;AAAA,EACA,MAAM,WAAW,KAAK;AACpB,QAAI,CAAC,SAAS,IAAI,GAAG,GAAG;AACtB,yBAAK,aAAc;AACnB,yBAAK,OAAQ;AACb,yBAAK,UAAS,IAAI,EAAE;AACpB;AAAA,IACF;AACA,UAAM,EAAE,SAAS,KAAK,IAAI,oBAAoB,IAAI,GAAG;AACrD,uBAAK,UAAS,IAAI,WAAW,EAAE;AAC/B,uBAAK,OAAQ,QAAQ;AACrB,uBAAK,aAAc;AAAA,EACrB;AAAA,EAyCA,YAAY;AACV,WAAO;AAAA,EACT;AAAA,EACA,cAAc;AACZ,UAAM,EAAE,YAAY,IAAI,mBAAK,MAAK,QAAQ,EAAE,aAAa,eAAe,IAAI,mBAAK,MAAK,QAAQ,eAAe,eAAe;AAC5H,WAAO;AAAA,MACL,OAAO,KAAK;AAAA,MACZ,QAAQ,KAAK;AAAA,MACb,OAAO,KAAK;AAAA,MACZ,UAAU,KAAK;AAAA,MACf,UAAU;AAAA,MACV,GAAG,KAAK;AAAA,MACR,UAAU,gBAAgB,CAAC,YAAY;AAAA,MACvC,aAAa;AAAA,MACb,aAAa,YAAY;AAAA,MACzB,KAAK,CAAC,KAAK;AAAA,IACb;AAAA,EACF;AAAA,EAySA,UAAU,SAAS,OAAO;AACxB,QAAI,QAAQ,OAAO;AACjB,4BAAK,sCAAL,WAAc,QAAQ,OAAO,QAAQ,MAAM;AAAA,IAC7C,WAAW,QAAQ,QAAQ;AACzB,4BAAK,uCAAL,WAAe,QAAQ,QAAQ,QAAQ,OAAO;AAAA,IAChD;AAAA,EACF;AAAA,EACA,SAAS;AAAA,EACT;AAsBF;AAveE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAfF;AA2GE,kBAAa,WAAG;AACd,wBAAK,oCAAL;AACA,QAAM,UAAU,mBAAK,UAAL;AAChB,MAAI,CAAC,SAAS;AACZ,SAAK,IAAI,IAAI,EAAE;AACf;AAAA,EACF;AACA,OAAK,IAAI,IAAI,GAAG,KAAK,UAAU,CAAC,UAAU,OAAO,EAAE;AACnD,qBAAK,MAAK,OAAO,YAAY;AAC/B;AACA,oBAAe,WAAG;AAChB,QAAM,UAAU,mBAAK,UAAL;AAChB,MAAI,CAAC,QAAS;AACd,QAAM,UAAU,gBAAgB,GAAG,QAAQ,IAAI,gBAAgB;AAC/D,qBAAK,mBAAoB;AACzB,oBAAkB,SAAS,OAAO,mBAAK,MAAK,EAAE,KAAK,CAAC,SAAS;AAC3D,YAAQ,QAAQ,IAAI;AAAA,EACtB,CAAC,EAAE,MAAM,CAAC,MAAM;AAlLpB;AAmLM,YAAQ,OAAO;AACf;AACE,+BAAK,MAAK,WAAV,mBAAkB,UAAU,6CAA6C,OAAO,OAAO,YAAY,SAAS,GAAG;AAAA,IACjH;AAAA,EACF,CAAC;AACD,SAAO,MAAM;AACX,YAAQ,OAAO;AACf,UAAM,MAAM;AAAA,EACd;AACF;AACA,cAAS,WAAG;AACV,QAAM,QAAQ,mBAAK,MAAL,YAAa,EAAE,QAAQ,UAAU,IAAI,mBAAK;AACxD,SAAO,mBAAmB,IAAI,KAAK;AACnC,YAAU,WAAW,WAAW,EAAE,CAAC,KAAK;AACxC,MAAI,OAAO;AACT,WAAO,YAAY,WAAW,UAAU,MAAM;AAlMpD;AAmMQ,UAAI,UAAU,KAAM;AACpB,YAAM,MAAK,eAAU,aAAV,mBAAoB;AAC/B,UAAI,GAAI,uBAAK,qCAAL,WAAa,cAAc;AAAA,IACrC,CAAC;AAAA,EACH;AACF;AAmBA,sBAAiB,WAAG;AAClB,wBAAK,qCAAL,WAAa;AACf;AAEA;AACA,kBAAa,SAAC,MAAM,SAAS;AAC3B,MAAI,mBAAK,wBAAuB,SAAS,EAAG;AAC5C,QAAM,EAAE,iBAAiB,QAAQ,aAAa,aAAa,KAAK,IAAI,mBAAK,MAAK;AAC9E,MAAI,gBAAgB,MAAM,KAAM;AAChC,QAAM,WAAW,gBAAgB;AACjC,qBAAK,MAAK,OAAO,eAAe,MAAM,OAAO;AAC7C,MAAI,KAAK,IAAI,WAAW,IAAI,IAAI,KAAK;AACnC,uBAAK,MAAK,OAAO,WAAW,MAAM,OAAO;AACzC,QAAI,CAAC,OAAO,KAAK,YAAY,IAAI,MAAM;AACrC,yBAAK,MAAK,OAAO,WAAW,QAAQ,OAAO;AAAA,IAC7C;AAAA,EACF;AACA,MAAI,CAAC,KAAK,KAAK,YAAY,IAAI,OAAO,MAAM;AAC1C,uBAAK,MAAK,OAAO,OAAO,QAAQ,OAAO;AACvC,uBAAK,qBAAsB;AAC3B,eAAW,MAAM;AACf,yBAAK,qBAAsB;AAAA,IAC7B,GAAG,GAAG;AAAA,EACR;AACF;AACA,cAAS,SAAC,MAAM,SAAS;AACvB,qBAAK,MAAK,OAAO,UAAU,MAAM,OAAO;AAC1C;AACA,cAAS,SAAC,SAAS;AAvPrB;AAwPI,QAAM,UAAU,mBAAK,UAAL;AAChB,2BAAK,uBAAL,mBAAwB,QAAQ,KAAK,CAAC,SAAS;AAC7C,QAAI,CAAC,KAAM;AACX,UAAM,EAAE,OAAO,QAAQ,UAAU,IAAI,IAAI;AACzC,uBAAK,MAAK,IAAI,GAAG;AACjB,uBAAK,MAAK,OAAO,gBAAgB,OAAO,OAAO;AAC/C,uBAAK,MAAK,OAAO,iBAAiB,QAAQ,OAAO;AACjD,uBAAK,MAAK,OAAO,mBAAmB,UAAU,OAAO;AACrD,0BAAK,sCAAL,WAAc,UAAU;AAAA,EAC1B,GAAG,MAAM,MAAM;AACb,QAAI,YAAY,mBAAK,UAAL,WAAiB;AACjC,0BAAK,qCAAL,WAAa;AACb,0BAAK,qCAAL,WAAa;AAAA,EACf;AACF;AACA,aAAQ,SAAC,UAAU,SAAS;AAC1B,QAAM,EAAE,eAAe,IAAI,mBAAK,MAAK,QAAQ,oBAAoB,eAAe;AAChF,qBAAK,gBAAiB,IAAI,UAAU,GAAG,QAAQ;AAC/C,QAAM,SAAS;AAAA,IACb,UAAU,IAAI,UAAU,GAAG,CAAC;AAAA,IAC5B,UAAU,mBAAK;AAAA,IACf;AAAA,EACF;AACA,qBAAK,MAAK,SAAS,MAAM,QAAQ,OAAO;AACxC,MAAI,CAAC,mBAAmB;AACtB,0BAAK,qCAAL,WAAa;AAAA,EACf;AACA,wBAAK,qCAAL,WAAa;AACb,wBAAK,qCAAL,WAAa;AACf;AACA,cAAS,SAAC,QAAQ,MAAM,SAAS;AAtRnC;AAuRI,UAAQ,QAAQ;AAAA,IACd,KAAK;AACH,YAAM,aAAa;AACnB,yBAAK,MAAK,OAAO,gBAAgB,YAAY,OAAO;AACpD;AAAA,IACF,KAAK;AACH,YAAM,WAAW;AACjB,UAAI,CAAC,mBAAK,MAAK,OAAO,QAAQ,GAAG;AAC/B,8BAAK,sCAAL,WAAc,UAAU;AAAA,MAC1B,OAAO;AACL,2BAAK,MAAK,OAAO,mBAAmB,UAAU,OAAO;AAAA,MACvD;AACA;AAAA,IACF,KAAK;AACH,4BAAK,2CAAL,WAAmB,MAAM;AACzB;AAAA,IACF,KAAK;AACH,UAAI,QAAQ,IAAI,KAAK,KAAK,QAAQ;AAChC,8BAAK,6CAAL,WAAqB,KAAK,KAAK,SAAS,CAAC,EAAE,CAAC,GAAG;AAAA,MACjD;AACA;AAAA,IACF,KAAK;AACH,4BAAK,6CAAL,WAAqB,KAAK,mBAAK,MAAK,OAAO,MAAM,GAAG,MAAM;AAC1D;AAAA;AAAA;AAAA;AAAA,IAIF,KAAK;AACH,4BAAK,+CAAL,WAAuB;AACvB;AAAA,IACF,KAAK;AACH,4BAAK,gDAAL,WAAwB,MAAM;AAC9B;AAAA,EACJ;AACA,8BAAK,yCAAL,WAAiB,YAAjB,mBAA0B;AAC5B;AACA,qBAAgB,WAAG;AACjB,aAAW,QAAQ,oBAAoB;AACrC,0BAAK,qCAAL,WAAa,oBAAoB;AAAA,EACnC;AACF;AACA,aAAQ,SAAC,SAAS;AAChB,qBAAK,UAAS,KAAK;AACnB,qBAAK,MAAK,OAAO,SAAS,QAAQ,OAAO;AAC3C;AACA,YAAO,SAAC,SAAS;AACf,qBAAK,UAAS,MAAM;AACpB,qBAAK,MAAK,OAAO,QAAQ,QAAQ,OAAO;AAC1C;AACA,oBAAe,SAAC,SAAS;AACvB,QAAM,EAAE,OAAO,IAAI,mBAAK,MAAK;AAC7B,MAAI,CAAC,OAAO,KAAK,CAAC,mBAAK,sBAAqB;AAC1C,uBAAK,MAAK,OAAO,WAAW,QAAQ,OAAO;AAAA,EAC7C;AACF;AACA,oBAAe,SAAC,UAAU,SAAS;AACjC,QAAM,SAAS;AAAA,IACb,UAAU,IAAI,UAAU,GAAG,QAAQ;AAAA,IACnC,UAAU,mBAAK;AAAA,EACjB;AACA,qBAAK,MAAK,OAAO,YAAY,QAAQ,OAAO;AAC9C;AACA,mBAAc,SAAC,SAAS;AACtB,qBAAK,MAAK,OAAO,WAAW,QAAQ,OAAO;AAC7C;AACA,iBAAY,SAAC,SAAS;AACpB,QAAM,EAAE,OAAO,IAAI,mBAAK,MAAK;AAC7B,MAAI,CAAC,OAAO,EAAG,oBAAK,MAAK,OAAO,WAAW,QAAQ,OAAO;AAC5D;AACA,eAAU,SAAC,SAAS;AAClB,QAAM,EAAE,OAAO,IAAI,mBAAK,MAAK;AAC7B,MAAI,OAAO,GAAG;AACZ,uBAAK,MAAK,OAAO,QAAQ,QAAQ,OAAO;AAAA,EAC1C;AACA,qBAAK,MAAK,OAAO,WAAW,QAAQ,OAAO;AAC7C;AACA,oBAAe,SAAC,QAAQ,OAAO,SAAS;AACtC,QAAM,SAAS,EAAE,QAAQ,MAAM;AAC/B,qBAAK,MAAK,OAAO,iBAAiB,QAAQ,OAAO;AACnD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAwBA,sBAAiB,SAAC,UAAU;AAC1B,wBAAK,6CAAL;AACA,MAAI,CAAC,SAAS,OAAQ;AACtB,QAAM,QAAQ,IAAI,UAAU;AAAA,IAC1B,MAAM;AAAA,IACN,SAAS;AAAA,EACX,CAAC,GAAG,EAAE,YAAY,IAAI,mBAAK,MAAK;AAChC,WAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,UAAM,UAAU,SAAS,CAAC,GAAG,cAAc,SAAS,IAAI,CAAC;AACzD,UAAM;AAAA,MACJ,IAAI,OAAO;AAAA,QACT,QAAQ;AAAA,SACR,2CAAa,cAAa,YAAY;AAAA,QACtC,QAAQ;AAAA,MACV;AAAA,IACF;AAAA,EACF;AACA,qBAAK,gBAAiB;AACtB,qBAAK,MAAK,WAAW,IAAI,KAAK;AAChC;AACA,oBAAe,WAAG;AAChB,MAAI,CAAC,mBAAK,gBAAgB;AAC1B,qBAAK,MAAK,WAAW,OAAO,mBAAK,eAAc;AAC/C,qBAAK,gBAAiB;AACxB;AACA,uBAAkB,SAAC,WAAW,SAAS;AACrC,qBAAK,MAAK,UAAU,cAAc,UAAU,IAAI,UAAU,KAAK,CAAC,MAAM,EAAE,OAAO,MAAM,IAAI,MAAM,sBAAK,qCAAL,WAAa,cAAc,UAAU;AACpI,aAAW,WAAW,WAAW;AAC/B,QAAI,QAAQ,OAAO,OAAQ;AAC3B,UAAM,SAAS,CAAC,QAAQ,GAAG,MAAM,GAAG,EAAE;AACtC,QAAI,MAAM,MAAM,EAAG;AACnB,uBAAK,MAAK,UAAU,WAAW,GAAG;AAAA,MAChC;AAAA,QACE,IAAI,QAAQ;AAAA,QACZ,OAAO,UAAU,KAAK;AAAA,QACtB;AAAA,QACA,OAAO;AAAA,QACP,SAAS;AAAA,MACX;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACA,wBAAK,8CAAL,WACE,UAAU,KAAK,CAAC,MAAM,EAAE,MAAM,GAC9B;AAEJ;AACA,qBAAgB,SAAC,EAAE,GAAG,IAAI,CAAC,GAAG,SAAS;AACrC,MAAI,CAAC,GAAI;AACT,QAAM,SAAS,OAAO,QAAQ,aAAa,mBAAK,MAAK,UAAU,QAAQ,EAAE;AACzE,MAAI,QAAQ;AACV,uBAAK,MAAK,UAAU,cAAc,OAAO,EAAE,QAAQ,OAAO;AAC1D,uBAAK,MAAK,UAAU,WAAW,MAAM,EAAE,QAAQ,MAAM,OAAO;AAAA,EAC9D,OAAO;AACL,uBAAK,MAAK,UAAU,WAAW,MAAM,EAAE,cAAc,QAAQ,MAAM,OAAO;AAAA,EAC5E;AACF;AACA,aAAQ,SAAC,OAAO,SAAS,SAAS;AAChC,UAAQ,OAAO;AAAA,IACb,KAAK;AACH,4BAAK,8CAAL;AACA;AAAA,IACF,KAAK;AACH,4BAAK,uCAAL,WAAe;AACf;AAAA,IACF,KAAK;AACH,4BAAK,qCAAL,WAAa;AACb;AAAA,IACF,KAAK;AACH,4BAAK,6CAAL,WAAqB;AACrB;AAAA,IACF,KAAK;AACH,4BAAK,sCAAL,WAAc;AACd;AAAA,IACF,KAAK;AACH,4BAAK,6CAAL,WAAqB,QAAQ,SAAS;AACtC;AAAA,IACF,KAAK;AACH,4BAAK,wCAAL,WAAgB;AAChB;AAAA,IACF,KAAK;AACH,4BAAK,4CAAL,WAAoB;AACpB;AAAA,IACF,KAAK;AACH,4BAAK,0CAAL,WAAkB;AAClB;AAAA,IACF,KAAK;AACH,4BAAK,6CAAL,WAAqB,QAAQ,QAAQ,KAAK,mBAAK,MAAK,OAAO,KAAK,GAAG;AACnE;AAAA,IACF,KAAK;AACH,yBAAK,gBAAiB,IAAI,UAAU,GAAG,QAAQ,QAAQ;AACvD,yBAAK,MAAK,OAAO,mBAAmB,QAAQ,UAAU,OAAO;AAC7D;AAAA,IACF,KAAK;AACH,yBAAK,MAAK,OAAO,eAAe,QAAQ,cAAc,OAAO;AAC7D;AAAA,IACF,KAAK;AACH,4BAAK,8CAAL,WAAsB,SAAS;AAC/B;AAAA,IACF,KAAK;AACH,yBAAK,mBAAoB,QAAQ;AACjC,yBAAK,MAAK,OAAO,qBAAqB,QAAQ,YAAY,OAAO;AACjE;AAAA,IACF,KAAK;AACH,yBAAK,MAAK,OAAO,6BAA6B,MAAM,OAAO;AAC3D;AAAA,IACF,KAAK;AACH,yBAAK,MAAK,OAAO,6BAA6B,OAAO,OAAO;AAC5D;AAAA,IACF,KAAK;AACH,yBAAK,MAAK,OAAO,OAAO,QAAQ,OAAO;AACvC;AAAA,IACF,KAAK;AACH,4BAAK,sCAAL,WAAc,SAAS;AACvB;AAAA,IACF,KAAK;AAAA,IACL,KAAK;AACH,4BAAK,uCAAL,WAAe,QAAQ,SAAS;AAChC;AAAA,EACJ;AACF;AACA,aAAQ,SAAC,OAAO,SAAS;AAvf3B;AAwfI,QAAM,EAAE,SAAS,OAAO,IAAI;AAC5B,MAAI,WAAW,mBAAmB;AAChC,uBAAK,MAAK,IAAI,KAAK;AAAA,EACrB;AACA,MAAI,QAAQ;AACV,gCAAK,yCAAL,WAAiB,YAAjB,mBAA0B,OAAO;AAAA,EACnC;AACA;AACE,6BAAK,MAAK,WAAV,mBAAkB,WAAW,YAAY,OAAO,IAAI,YAAY,SAAS,OAAO,YAAY,YAAY,MAAM,YAAY,SAAS,SAAS;AAAA,EAC9I;AACF;AAUM,YAAO,eAAC,SAAS,KAAK;AAC1B,MAAI,UAAU,gBAAgB,GAAG,WAAW,mBAAK,WAAU,IAAI,OAAO;AACtE,MAAI,CAAC,SAAU,oBAAK,WAAU,IAAI,SAAS,WAAW,CAAC,CAAC;AACxD,WAAS,KAAK,OAAO;AACrB,OAAK,YAAY;AAAA,IACf,QAAQ;AAAA,IACR,OAAO;AAAA,EACT,CAAC;AACD,SAAO,QAAQ;AACjB;AACA,WAAM,WAAG;AACP,qBAAK,UAAS,KAAK;AACnB,qBAAK,gBAAiB,IAAI,UAAU,GAAG,CAAC;AACxC,qBAAK,mBAAoB;AACzB,qBAAK,aAAc;AACnB,qBAAK,MAAK,IAAI,KAAK;AACnB,wBAAK,6CAAL;AACF;AACA,gBAAW,SAAC,SAAS;AA9hBvB;AA+hBI,UAAO,wBAAK,WAAU,IAAI,OAAO,MAA1B,mBAA6B;AACtC;", "names": []}