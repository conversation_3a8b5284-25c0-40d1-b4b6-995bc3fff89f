"use client";
import {
  HTMLAirPlayAdapter,
  HTMLMediaProvider
} from "./chunk-5Q2PE4LT.js";
import {
  scoped
} from "./chunk-DSWAFM5W.js";
import "./chunk-Y4LBKCPT.js";
import {
  require_react
} from "./chunk-ZMLY2J2T.js";
import {
  __publicField,
  __toESM
} from "./chunk-4B2QHNJT.js";

// node_modules/@vidstack/react/dev/chunks/vidstack-CvY7pagE.js
var import_react = __toESM(require_react(), 1);
var AudioProvider = class extends HTMLMediaProvider {
  constructor(audio, ctx) {
    super(audio, ctx);
    __publicField(this, "$$PROVIDER_TYPE", "AUDIO");
    __publicField(this, "airPlay");
    scoped(() => {
      this.airPlay = new HTMLAirPlayAdapter(this.media, ctx);
    }, this.scope);
  }
  get type() {
    return "audio";
  }
  setup() {
    super.setup();
    if (this.type === "audio") this.ctx.notify("provider-setup", this);
  }
  /**
   * The native HTML `<audio>` element.
   *
   * @see {@link https://developer.mozilla.org/en-US/docs/Web/API/HTMLAudioElement}
   */
  get audio() {
    return this.media;
  }
};
export {
  AudioProvider
};
//# sourceMappingURL=vidstack-CvY7pagE-V3S4OB45.js.map
