import { useState, useEffect, useRef } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { VideoPlayer as Player } from "@/components/ui/video-player";
import { videoApi, VideoFile } from "@/lib/videoApi";
import { analyticsApi } from "@/lib/analyticsApi";
import {
  ArrowLeft,
  Clock,
  AlertTriangle,
  Calendar,
  FileVideo,
  MonitorPlay,
  Layers,
  HardDrive,
  Eye,
  Share2,
  Download,
  Heart,
  MoreHorizontal,
  Play,
  Loader,
} from "lucide-react";
import type { PlaybackInfo } from "@/components/ui/video-player";
import { motion } from "framer-motion";

export default function VideoPlayer() {
  const { videoId } = useParams<{ videoId: string }>();
  const navigate = useNavigate();
  const [video, setVideo] = useState<VideoFile | null>(null);
  const [playbackInfo, setPlaybackInfo] = useState<PlaybackInfo | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Analytics tracking
  const [sessionId, setSessionId] = useState<string | null>(null);
  const [watchStartTime, setWatchStartTime] = useState<number | null>(null);
  const watchDurationRef = useRef<number>(0);
  const lastHeartbeatRef = useRef<number>(0);

  // Record a video view when the component mounts
  useEffect(() => {
    const recordView = async () => {
      if (videoId) {
        try {
          await analyticsApi.recordVideoView(videoId, 0); // Initial view with 0 duration
          console.log("Video view recorded");
        } catch (error) {
          console.error("Failed to record video view:", error);
        }
      }
    };

    recordView();
  }, [videoId]);

  // Start a watch session when the video starts playing
  const startWatchSession = async () => {
    if (videoId && !sessionId) {
      try {
        const session = await analyticsApi.startWatchSession(videoId);
        setSessionId(session.session_id);
        setWatchStartTime(Date.now());
        watchDurationRef.current = 0;
        lastHeartbeatRef.current = Date.now();
        console.log("Watch session started:", session.session_id);
      } catch (error) {
        console.error("Failed to start watch session:", error);
      }
    }
  };

  // End the watch session when the component unmounts or when the video ends
  const endWatchSession = async (completed = false) => {
    if (videoId && sessionId && watchStartTime) {
      try {
        // Calculate watch duration in seconds
        const watchDuration = Math.floor(watchDurationRef.current / 1000);

        await analyticsApi.endWatchSession(sessionId, watchDuration, completed);
        console.log("Watch session ended:", {
          sessionId,
          watchDuration,
          completed,
        });

        // Reset session state
        setSessionId(null);
        setWatchStartTime(null);
        watchDurationRef.current = 0;
      } catch (error) {
        console.error("Failed to end watch session:", error);
      }
    }
  };

  // Update watch duration periodically
  useEffect(() => {
    if (sessionId && watchStartTime) {
      const interval = setInterval(() => {
        const now = Date.now();
        const elapsed = now - lastHeartbeatRef.current;
        watchDurationRef.current += elapsed;
        lastHeartbeatRef.current = now;
      }, 1000);

      return () => {
        clearInterval(interval);
        // End the session when the component unmounts
        endWatchSession(false);
      };
    }
  }, [sessionId, watchStartTime]);

  useEffect(() => {
    const fetchVideoData = async () => {
      try {
        setLoading(true);
        const videoData = await videoApi.getVideo(videoId!);
        setVideo(videoData);

        if (videoData.status === "completed") {
          const playbackData = await videoApi.getPlaybackInfo(videoId!);
          setPlaybackInfo({
            ...playbackData,
            error_message: "",
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          });
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : "Failed to load video");
      } finally {
        setLoading(false);
      }
    };

    fetchVideoData();
  }, [videoId]);

  if (loading) {
    return (
      <div className="min-h-[70vh] flex flex-col justify-center items-center">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5 }}
          className="relative w-24 h-24"
        >
          <div className="absolute inset-0 rounded-full bg-gradient-to-br from-violet-600 to-fuchsia-600 opacity-20 blur-xl animate-pulse"></div>
          <div className="absolute inset-2 rounded-full border-4 border-violet-500/30 border-t-violet-500 animate-spin"></div>
          <div className="absolute inset-0 flex items-center justify-center">
            <Loader className="w-8 h-8 text-violet-400 animate-pulse" />
          </div>
        </motion.div>
        <p className="mt-6 text-slate-400 font-medium">
          Loading video player...
        </p>
      </div>
    );
  }

  if (error || !video) {
    return (
      <div className="min-h-[70vh] flex flex-col justify-center items-center px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <Card className="p-8 bg-gradient-to-br from-red-950/40 to-red-900/10 backdrop-blur-xl border border-red-500/20 max-w-md mx-auto shadow-xl">
            <div className="flex flex-col items-center text-center">
              <div className="w-20 h-20 rounded-full bg-gradient-to-br from-red-500/20 to-red-600/10 flex items-center justify-center mb-6 shadow-lg shadow-red-500/10 border border-red-500/20">
                <AlertTriangle className="w-10 h-10 text-red-400" />
              </div>
              <h2 className="text-2xl font-bold text-white mb-3 bg-clip-text text-transparent bg-gradient-to-r from-red-300 to-red-100">
                Failed to Load Video
              </h2>
              <p className="text-slate-300 mb-6 leading-relaxed">{error}</p>
              <Button
                variant="outline"
                onClick={() => navigate("/dashboard")}
                className="border-red-500/30 hover:bg-red-500/10 hover:border-red-500/50 text-red-100 px-6 py-2 rounded-full transition-all duration-300 shadow-md shadow-red-950/20"
                aria-label="Back to Dashboard"
                title="Back to Dashboard"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Dashboard
              </Button>
            </div>
          </Card>
        </motion.div>
      </div>
    );
  }

  if (video.status !== "completed") {
    return (
      <div className="max-w-5xl mx-auto px-4">
        <div className="flex items-center mb-8">
          <Button
            variant="ghost"
            onClick={() => navigate("/dashboard")}
            className="text-slate-400 hover:text-white group transition-all duration-300"
            aria-label="Back to Dashboard"
            title="Back to Dashboard"
          >
            <ArrowLeft className="w-4 h-4 mr-2 group-hover:-translate-x-1 transition-transform duration-300" />
            Back to Dashboard
          </Button>
        </div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <Card className="p-10 bg-gradient-to-br from-violet-950/30 to-fuchsia-950/20 backdrop-blur-xl border border-violet-500/20 shadow-xl overflow-hidden relative">
            <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-violet-500 to-fuchsia-500 opacity-70"></div>
            <div className="absolute -top-24 -right-24 w-64 h-64 bg-violet-600/10 rounded-full blur-3xl"></div>
            <div className="absolute -bottom-32 -left-32 w-80 h-80 bg-fuchsia-600/10 rounded-full blur-3xl"></div>

            <div className="flex flex-col items-center text-center relative z-10">
              <div className="w-24 h-24 rounded-2xl bg-gradient-to-br from-violet-500/20 to-fuchsia-500/20 flex items-center justify-center mb-8 shadow-lg shadow-violet-500/10 border border-violet-500/20 relative">
                <div className="absolute inset-0 bg-violet-500/5 rounded-2xl animate-pulse"></div>
                <Clock className="w-12 h-12 text-violet-400 animate-spin" />
              </div>

              <h2 className="text-3xl font-bold tracking-tight text-white bg-clip-text text-transparent bg-gradient-to-r from-violet-400 to-fuchsia-400 mb-4">
                Video Processing
              </h2>

              <div className="w-16 h-1 bg-gradient-to-r from-violet-500 to-fuchsia-500 rounded-full mb-6 opacity-70"></div>

              <p className="text-slate-300 text-lg mb-8 max-w-lg leading-relaxed">
                Your video is currently being processed and optimized for
                streaming. This may take a few minutes depending on the file
                size and complexity.
              </p>

              <div className="w-full max-w-lg mb-8 bg-black/20 h-2 rounded-full overflow-hidden">
                <motion.div
                  className="h-full bg-gradient-to-r from-violet-500 to-fuchsia-500"
                  initial={{ width: "5%" }}
                  animate={{ width: "60%" }}
                  transition={{
                    duration: 3,
                    repeat: Infinity,
                    repeatType: "reverse",
                    ease: "easeInOut",
                  }}
                ></motion.div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 w-full max-w-lg">
                <Card className="p-5 bg-black/30 backdrop-blur-sm border-violet-500/10 hover:border-violet-500/30 transition-all duration-300 shadow-lg">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 rounded-lg bg-violet-500/10 flex items-center justify-center">
                      <FileVideo className="w-5 h-5 text-violet-400" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-slate-400 mb-1">
                        File Name
                      </p>
                      <p className="text-white font-semibold tracking-wide truncate">
                        {video.file_name}
                      </p>
                    </div>
                  </div>
                </Card>

                <Card className="p-5 bg-black/30 backdrop-blur-sm border-violet-500/10 hover:border-violet-500/30 transition-all duration-300 shadow-lg">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 rounded-lg bg-violet-500/10 flex items-center justify-center">
                      <HardDrive className="w-5 h-5 text-violet-400" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-slate-400 mb-1">
                        File Size
                      </p>
                      <p className="text-white font-semibold tracking-wide">
                        {Math.round(video.file_size / (1024 * 1024))} MB
                      </p>
                    </div>
                  </div>
                </Card>

                <Card className="p-5 bg-black/30 backdrop-blur-sm border-violet-500/10 hover:border-violet-500/30 transition-all duration-300 shadow-lg">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 rounded-lg bg-violet-500/10 flex items-center justify-center">
                      <Calendar className="w-5 h-5 text-violet-400" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-slate-400 mb-1">
                        Uploaded
                      </p>
                      <p className="text-white font-semibold tracking-wide">
                        {new Date(video.uploaded_at).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                </Card>
              </div>
            </div>
          </Card>
        </motion.div>
      </div>
    );
  }

  if (!playbackInfo) {
    return null;
  }

  return (
    <div className="max-w-6xl mx-auto px-4 pb-16">
      <div className="flex items-center justify-between mb-8">
        <Button
          variant="ghost"
          onClick={() => navigate("/dashboard")}
          className="text-slate-400 hover:text-white group transition-all duration-300"
          aria-label="Back to Dashboard"
          title="Back to Dashboard"
        >
          <ArrowLeft className="w-4 h-4 mr-2 group-hover:-translate-x-1 transition-transform duration-300" />
          Back to Dashboard
        </Button>

        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            className="text-slate-400 hover:text-white p-2 h-9 w-9"
            aria-label="Share Video"
            title="Share Video"
          >
            <Share2 className="w-4 h-4" />
          </Button>
          <Button
            variant="ghost"
            className="text-slate-400 hover:text-white p-2 h-9 w-9"
            aria-label="Download Video"
            title="Download Video"
          >
            <Download className="w-4 h-4" />
          </Button>
          <Button
            variant="ghost"
            className="text-slate-400 hover:text-white p-2 h-9 w-9"
            aria-label="Like Video"
            title="Like Video"
          >
            <Heart className="w-4 h-4" />
          </Button>
          <Button
            variant="ghost"
            className="text-slate-400 hover:text-white p-2 h-9 w-9"
            aria-label="More Options"
            title="More Options"
          >
            <MoreHorizontal className="w-4 h-4" />
          </Button>
        </div>
      </div>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="space-y-8"
      >
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
          <h1 className="text-2xl md:text-3xl font-bold tracking-tight text-white bg-clip-text text-transparent bg-gradient-to-r from-violet-400 to-fuchsia-400">
            {video.file_name}
          </h1>

          <div className="flex items-center gap-3 text-sm text-slate-400">
            <div className="flex items-center gap-1">
              <Eye className="w-4 h-4 text-violet-400" />
              <span>{Math.floor(Math.random() * 1000) + 100} views</span>
            </div>
            <div className="w-1 h-1 rounded-full bg-slate-700"></div>
            <div className="flex items-center gap-1">
              <Calendar className="w-4 h-4 text-violet-400" />
              <span>{new Date(video.uploaded_at).toLocaleDateString()}</span>
            </div>
          </div>
        </div>

        <Card className="bg-gradient-to-br from-black/60 to-black/40 backdrop-blur-xl border-violet-500/10 overflow-hidden shadow-xl rounded-xl">
          <div className="relative">
            <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent pointer-events-none z-10"></div>
            <Player
              playbackInfo={playbackInfo}
              onPlay={() => startWatchSession()}
              onEnded={() => endWatchSession(true)}
            />
          </div>
        </Card>

        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 md:gap-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
          >
            <Card className="p-5 bg-gradient-to-br from-black/60 to-black/40 backdrop-blur-xl border-violet-500/10 hover:border-violet-500/30 transition-all duration-300 shadow-lg rounded-xl overflow-hidden group">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 rounded-lg bg-violet-500/10 flex items-center justify-center group-hover:bg-violet-500/20 transition-colors duration-300">
                  <MonitorPlay className="w-5 h-5 text-violet-400" />
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-slate-400 mb-1">
                    Resolution
                  </p>
                  <p className="text-white font-semibold tracking-wide">
                    {Object.keys(playbackInfo.qualities)[0]}
                  </p>
                </div>
              </div>
            </Card>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <Card className="p-5 bg-gradient-to-br from-black/60 to-black/40 backdrop-blur-xl border-violet-500/10 hover:border-violet-500/30 transition-all duration-300 shadow-lg rounded-xl overflow-hidden group">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 rounded-lg bg-violet-500/10 flex items-center justify-center group-hover:bg-violet-500/20 transition-colors duration-300">
                  <HardDrive className="w-5 h-5 text-violet-400" />
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-slate-400 mb-1">
                    Size
                  </p>
                  <p className="text-white font-semibold tracking-wide">
                    {Math.round(video.file_size / (1024 * 1024))} MB
                  </p>
                </div>
              </div>
            </Card>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            <Card className="p-5 bg-gradient-to-br from-black/60 to-black/40 backdrop-blur-xl border-violet-500/10 hover:border-violet-500/30 transition-all duration-300 shadow-lg rounded-xl overflow-hidden group">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 rounded-lg bg-violet-500/10 flex items-center justify-center group-hover:bg-violet-500/20 transition-colors duration-300">
                  <Clock className="w-5 h-5 text-violet-400" />
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-slate-400 mb-1">
                    Duration
                  </p>
                  <p className="text-white font-semibold tracking-wide">
                    {Math.floor(playbackInfo.duration / 60)}:
                    {String(Math.floor(playbackInfo.duration % 60)).padStart(
                      2,
                      "0"
                    )}
                  </p>
                </div>
              </div>
            </Card>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
          >
            <Card className="p-5 bg-gradient-to-br from-black/60 to-black/40 backdrop-blur-xl border-violet-500/10 hover:border-violet-500/30 transition-all duration-300 shadow-lg rounded-xl overflow-hidden group">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 rounded-lg bg-violet-500/10 flex items-center justify-center group-hover:bg-violet-500/20 transition-colors duration-300">
                  <Layers className="w-5 h-5 text-violet-400" />
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-slate-400 mb-1">
                    Format
                  </p>
                  <p className="text-white font-semibold tracking-wide">
                    {playbackInfo.format.toUpperCase()}
                  </p>
                </div>
              </div>
            </Card>
          </motion.div>
        </div>

        <Card className="p-6 bg-gradient-to-br from-black/60 to-black/40 backdrop-blur-xl border-violet-500/10 shadow-lg rounded-xl overflow-hidden">
          <h2 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
            <Play className="w-5 h-5 text-violet-400" />
            Video Information
          </h2>
          <p className="text-slate-300 leading-relaxed">
            This video was uploaded on{" "}
            {new Date(video.uploaded_at).toLocaleDateString()} and is available
            in {Object.keys(playbackInfo.qualities).length} different quality
            options. The video is encoded in {playbackInfo.format.toUpperCase()}{" "}
            format for optimal streaming performance.
          </p>
        </Card>
      </motion.div>
    </div>
  );
}
